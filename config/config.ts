
import { defineConfig } from 'umi';
import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV } = process.env;
//项目公共配置文件，项目最终的配置，就是此文件配置加上面各环境下的配置覆盖合并后的配置
export default defineConfig({
  hash: true,
  history: { type: 'hash' },
  antd: {},
  dva: {
    hmr: true,
  },
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  dynamicImport: {
    loading: '@ant-design/pro-layout/es/PageLoading',
  },
  targets: {
    ie: 11,
  },
  // umi routes: https://umijs.org/docs/routing
  routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
    // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
    // https://ant.design/docs/react/customize-theme-variable-cn
    'root-entry-name': 'variable',
  },
  // esbuild is father build tools
  // https://umijs.org/plugins/plugin-esbuild
  esbuild: {},
  title: false,
  ignoreMomentLocale: true,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  manifest: {
    basePath: '/',
  },
  // Fast Refresh 热更新
  fastRefresh: {},
  nodeModulesTransform: { type: 'none' },
  mfsu: {},
  // extraBabelPlugins: ['react-activation/babel'],
  webpack5: {},
  // exportStatic: {},  //如果不关闭这个，build会报错，https://github.com/ant-design/ant-design-pro/issues/7638
  // 生产环境去除console日志打印
  terserOptions: {
    compress: {
      drop_console: process.env.NODE_ENV === 'dev' ? false : true,
    },
  },
  /**
   * @description: openAPI
   * @return {*}
   */
  // openAPI: {
  //   requestLibPath: "import request from '@/utils/request';",
  //   // 使用在线的版本
  //   schemaPath: "http://**************:8302/v3/api-docs",
  //   // schemaPath: join(__dirname, 'oneapi.json'),
  //   mock: false,
  //   projectName: 'swagger' // 生成文件名
  // },
  define: {
    API_URL: '',
    APP_NAME: 'common-admin-react',
    AES_ENCRYPT_KEY: 'unicom-ai-unicom'
  },
});
