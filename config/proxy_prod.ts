/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    '/bwp': {
      // 流程图
      target: 'http://*************:2023',
      changeOrigin: true,
    },
    '/api/cutover': {
      target: 'http://*************:11012',
      // 流程流转服务
      // target: 'http://*************:8086',
      // target: 'http://localhost:8086', //本机
      changeOrigin: true,
      pathRewrite: { '^/api/cutover': '/qsmzq-webservice/cutover' },
    },
    '/api/qsmzq-webservice': {
      target: 'http://*************:11012',
      // 流程流转服务
      // target: 'http://*************:8086',
      // target: 'http://localhost:8086', //本机
      changeOrigin: true,
      pathRewrite: { '^/api/qsmzq-webservice': '' },
    },
    // '/api/wo-resource-query': {
    //   // 公共资源接口服务
    //   target: 'http://*************:58302',
    //   changeOrigin: true,
    //   pathRewrite: { '^/api/wo-resource-query': '' },
    // },
    '/api/qsmzq-common': {
      // 公共资源接口服务
      // target: 'http://*************:8785',
      target: 'http://*************:11013',
      // target: 'http://************:8785',
      changeOrigin: true,
      pathRewrite: { '^/api/qsmzq-common': '' },
    },
    // '/api/common-admin-service': {
    //   // 拟稿导入
    //   target: 'http://*************:58302',
    //   changeOrigin: true,
    //   pathRewrite: { '^/api/common-admin-service': '' },
    // },
    '/api/': {
       target: 'http://*************:11012', //天宫测试环境
      // target: 'http://*************:8086',//公司
    //  target: 'http://localhost:8086',//公司
      // target: 'http://************:8087', //本机
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      //222
      changeOrigin: true,
      pathRewrite: { '^/api/': '' },
    },
    '/tianyan': {
      target: 'http://*************:9664/tianyan/',
      changeOrigin: true,
      pathRewrite: { '^/tianyan': '' },
    },
  },
};
