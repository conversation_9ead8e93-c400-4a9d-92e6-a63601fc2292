# 问题支撑SDK使用说明

## 概述

问题支撑SDK是一个React组件，用于在业务系统中嵌入网络创新服务支撑平台的问题支撑功能。通过悬浮窗的形式提供意见反馈、待办、待阅、查询等功能。

## 能力信息

- **能力名称**: 网络创新服务支撑平台-问题支撑接入能力-测试环境v1.0
- **能力ID**: 2504101432184938880
- **系统编码**: mTkrm9/zqBxN8
- **API版本**: v1.0

## 支持的网络环境

| 网络环境 | 接口地址 | 描述 |
|---------|---------|------|
| 天宫资源池 | https://172.24.123.29:2443 | 默认环境 |
| DCN映射 | https://10.186.1.29:2443 | DCN映射环境 |
| B网（OSS_VPN） | https://10.160.4.243:2443 | B网OSS_VPN环境 |
| B网（IOT_BSS） | https://************ | B网IOT_BSS环境 |

## 使用方法

### 基本使用

在BasicLayout.tsx中已经集成了SupportSDK组件：

```jsx
import SupportSDK from '@/components/framework/SupportSDK';

// 使用默认配置
<SupportSDK />
```

### 组件属性

| 属性 | 类型 | 默认值 | 描述 |
|-----|------|-------|------|
| systemCode | string | 'mTkrm9/zqBxN8' | 系统编码 |
| network | NetworkEnvironment | 'tiangong' | 网络环境 |

### 网络环境类型

```typescript
type NetworkEnvironment = 'tiangong' | 'dcn' | 'bnet_oss' | 'bnet_iot';
```

## 功能特性

1. **意见反馈**: 点击可直接跳转至服务支撑发单页面
2. **待办提醒**: 显示用户待办事项数量，点击后跳转至服务支撑平台处理
3. **待阅提醒**: 显示用户待阅事项数量，点击后跳转至服务支撑平台处理
4. **工单查询**: 用户可以查询问题工单状态

## 配置说明

### localStorage配置

组件会自动从localStorage中读取以下配置：

- `userName`: 用户名
- `sys`: 系统编码
- `network`: 网络环境

如果localStorage中没有这些配置，组件会使用默认值并自动保存到localStorage。

### 自动网络检测

组件支持自动检测最佳网络环境，目前默认使用天宫资源池环境。

## 开发调试

组件包含详细的控制台日志，可以通过浏览器开发者工具查看：

- 组件挂载信息
- 用户信息传递
- 消息接收和处理
- localStorage操作

## 注意事项

1. 确保用户名与O域用户中心的账号一致
2. 确保网络能够访问对应的服务支撑平台地址
3. 如果遇到跨域问题，可能需要配置代理或CORS
4. 组件会在页面右下角显示悬浮窗，请确保不与其他UI元素冲突

## 故障排查

1. **悬浮窗不显示**: 检查控制台是否有JavaScript错误
2. **无法跳转**: 检查网络连接和URL配置
3. **用户信息传递失败**: 检查localStorage中的用户名配置
4. **样式问题**: 检查CSS样式是否被其他样式覆盖

## 联系方式

如有问题，请联系：
- **产品负责人**: 刘露平
- **联系电话**: 18610800503

## 文件结构

```
src/components/framework/SupportSDK/
├── index.tsx          # 主组件文件
├── index.less         # 样式文件
└── config.ts          # 配置文件
```

## 更新日志

### v1.0.0
- 初始版本，支持基本的问题支撑功能
- 支持多网络环境切换
- 自动用户信息传递
- 完整的localStorage管理
