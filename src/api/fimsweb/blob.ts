export default function handleBlob(res: any) {
  const response: any = res.response.clone();
  return new Promise((resolve: any) => {
    const blob = res.data;
    const reader = new FileReader();
    reader.readAsText(blob);
    reader.onload = (e: any) => {
      let isFile = false;
      let resData;
      try {
        resData = JSON.parse(e.target.result);
        if (!Object.prototype.hasOwnProperty.call(resData, 'status')) {
          isFile = true;
        }
      } catch (err) {
        isFile = true;
      }
      if (!isFile) {
        return resolve(resData);
      } else {
        return handleFileDownload(response, blob, resolve);
      }
    };
  });
}

function handleFileDownload(response: any, blob: any, resolve: any) {
  const filename = getFileName(response);
  const nav: any = window.navigator;
  if (nav.msSaveOrOpenBlob) {
    nav.msSaveBlob(blob, filename);
  } else {
    const url = window.URL.createObjectURL(new Blob([blob]));
    const a = document.createElement('a');
    a.style.display = 'none';
    a.download = filename;
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
  return resolve({ status: '0', data: 'success' });
}

function getFileName(response: any) {
  let filename: any = '下载';
  const contentDispositon = response.headers.get('content-disposition');
  if (contentDispositon) {
    filename = decodeURIComponent(contentDispositon?.split('=')[1]);
  }
  return filename ?? '下载';
}
