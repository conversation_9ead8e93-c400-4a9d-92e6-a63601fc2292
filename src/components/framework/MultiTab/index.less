@import '~antd/es/style/themes/default.less';

@multi-tab-prefix-cls: ballcat-multi-tab;
@multi-tabs-height: 40px;
@multi-tabs-tab-height: 32px;

.ballcat-multi-tab {
  z-index: 16;
  height: @multi-tabs-height;
  margin-right: -24px;
  margin-left: -24px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  transition: width 0.3s;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: 'none';

  &-fixed {
    position: fixed;
    top: 48px;
  }

  &-float {
    position: relative;
    top: -24px;
  }
}

.ballcat-multi-tab .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap .ant-tabs-tab {
  height: @multi-tabs-height;
  margin: 0;
  padding: 0;
  line-height: @multi-tabs-height;
  background: none;
  border: none;
  border-radius: 0;
  transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
    color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ballcat-multi-tab .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap .ant-tabs-tab > div {
  padding: 0 0 0 16px;
}

.ballcat-multi-tab .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap .ant-tabs-tab-active {
  background-color: @primary-1;
}

.ballcat-multi-tab .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap .ant-tabs-tab .ant-tabs-tab-remove {
  height: @multi-tabs-height;
  margin: -4px 0 0 0;
}

.ballcat-multi-tab .ant-tabs .ballcat-multi-tab-tool {
  display: inline-block;
  width: @multi-tabs-height;
  height: @multi-tabs-height;
  line-height: @multi-tabs-height;
  text-align: center;
  border-left: 0.8px solid #efebeb;
  cursor: pointer;

  .anticon {
    font-size: 14px;
  }
}

.ballcat-multi-tab .ant-tabs-nav-operations .ant-tabs-nav-more {
  cursor: pointer;
}

// 卡片标签处理
.ballcat-multi-tab-card {
  height: @multi-tabs-height;
  margin-right: 0;
  margin-left: 0;
  padding-top: 8px;
  line-height: @multi-tabs-height;
  background-color: @layout-body-background;
  box-shadow: none;

  .ant-tabs .ant-tabs-nav {
    height: @multi-tabs-tab-height;

    .ant-tabs-extra-content {
      background-color: @white;

      .ballcat-multi-tab-tool {
        height: @multi-tabs-tab-height;
      }
    }

    .ant-tabs-nav-wrap {
      .ant-tabs-tab + .ant-tabs-tab {
        margin-left: 8px;
      }

      .ant-tabs-tab {
        height: 32px;
        line-height: 32px;
        background-color: @body-background;
        border-radius: 4px;
      }
    }
  }
}
