/**
 * 问题支撑SDK配置文件
 * 
 * 包含不同网络环境的接口地址配置
 */

// 网络环境类型
export type NetworkEnvironment = 'tiangong' | 'dcn' | 'bnet_oss' | 'bnet_iot';

// 网络环境配置
export interface NetworkConfig {
  name: string;
  baseUrl: string;
  description: string;
}

// 网络环境配置映射 - 使用完整的URL地址
export const NETWORK_CONFIGS: Record<NetworkEnvironment, NetworkConfig> = {
  // 天宫资源池
  tiangong: {
    name: '天宫资源池',
    baseUrl: 'https://*************:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0',
    description: '天宫资源池环境'
  },
  // DCN映射
  dcn: {
    name: 'DCN映射',
    baseUrl: 'https://***********:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0',
    description: 'DCN映射环境'
  },
  // B网（OSS_VPN）
  bnet_oss: {
    name: 'B网（OSS_VPN）',
    baseUrl: 'https://************:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0',
    description: 'B网OSS_VPN环境'
  },
  // B网（IOT_BSS）
  bnet_iot: {
    name: 'B网（IOT_BSS）',
    baseUrl: 'https://************/api/middlleplatform/mTkrm9/zqBxN8/v1.0',
    description: 'B网IOT_BSS环境'
  }
};

// 默认网络环境
export const DEFAULT_NETWORK: NetworkEnvironment = 'tiangong';

// 系统编码路径（已包含完整路径）
export const SYSTEM_CODE_PATH = 'mTkrm9/zqBxN8';

// API版本
export const API_VERSION = 'v1.0';

// 能力ID
export const CAPABILITY_ID = '2504101432184938880';

/**
 * 获取指定网络环境的完整API地址
 * @param network 网络环境
 * @returns 完整的API地址
 */
export function getApiUrl(
  network: NetworkEnvironment = DEFAULT_NETWORK
): string {
  const config = NETWORK_CONFIGS[network];
  return config.baseUrl;
}

/**
 * 根据当前网络环境自动选择最佳的接口地址
 * @returns 最佳的网络环境
 */
export function detectBestNetwork(): NetworkEnvironment {
  // 这里可以根据实际网络情况进行检测
  // 目前默认返回天宫资源池
  return DEFAULT_NETWORK;
}

/**
 * 获取所有可用的网络环境列表
 * @returns 网络环境配置数组
 */
export function getAvailableNetworks(): Array<{ network: NetworkEnvironment; config: NetworkConfig }> {
  return Object.entries(NETWORK_CONFIGS).map(([key, config]) => ({
    network: key as NetworkEnvironment,
    config
  }));
}
