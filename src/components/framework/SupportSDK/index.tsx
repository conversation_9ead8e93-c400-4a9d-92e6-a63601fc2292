import React, { useEffect, useState } from 'react';
import { getStore } from '@/utils/framework/store';
import { useModel } from 'umi';
import styles from './index.less';
import { getApiUrl, SYSTEM_CODE_PATH, detectBestNetwork, type NetworkEnvironment } from './config';

interface SupportSDKProps {
  systemCode?: string;
  network?: NetworkEnvironment;
}

/**
 * 问题支撑SDK悬浮窗组件
 *
 * 该组件通过iframe嵌入问题支撑平台的菜单页面，实现悬浮窗效果
 * 支持意见反馈、待办、待阅、查询等功能
 */
const SupportSDK: React.FC<SupportSDKProps> = ({ systemCode, network }) => {
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  // 获取当前用户信息 - 优先从localStorage获取，符合文档要求
  const localStorageUserName = localStorage.getItem("userName");
  const userName = localStorageUserName || currentUser?.user?.username || getStore('userName') || '';

  // 获取系统编码 - 优先从localStorage获取，符合文档要求
  const localStorageSys = localStorage.getItem("sys");
  const sys = localStorageSys || systemCode || SYSTEM_CODE_PATH;

  // 获取网络环境 - 优先从localStorage获取，然后自动检测
  const localStorageNetwork = localStorage.getItem("network") as NetworkEnvironment;
  const currentNetwork = localStorageNetwork || network || detectBestNetwork();

  // 生成完整的API地址
  const apiUrl = getApiUrl(currentNetwork);

  // 组件挂载时记录信息并确保localStorage中有用户名、系统编码和网络环境
  useEffect(() => {
    console.log('SupportSDK: 组件已挂载', {
      userName,
      sys,
      currentNetwork,
      apiUrl,
      currentUser
    });
    console.log('SupportSDK: 实际使用的iframe URL:', apiUrl);

    // 如果localStorage中没有用户名、系统编码和网络环境，则将当前值存入localStorage
    if (!localStorage.getItem("userName") && userName) {
      localStorage.setItem("userName", userName);
      console.log('SupportSDK: 已将用户名存入localStorage', userName);
    }

    if (!localStorage.getItem("sys") && sys) {
      localStorage.setItem("sys", sys);
      console.log('SupportSDK: 已将系统编码存入localStorage', sys);
    }

    if (!localStorage.getItem("network") && currentNetwork) {
      localStorage.setItem("network", currentNetwork);
      console.log('SupportSDK: 已将网络环境存入localStorage', currentNetwork);
    }
  }, [userName, sys, currentNetwork, apiUrl]);

  // iframe加载完成后发送用户信息
  const handleIframeLoad = () => {
    setIframeLoaded(true);
    // 使用文档要求的id 'button'
    const iframe = document.getElementById('button') as HTMLIFrameElement;
    if (iframe && iframe.contentWindow) {
      console.log('SupportSDK: 发送用户信息', { userName, sys });
      iframe.contentWindow.postMessage({
        type: "sendUserName",
        userName: userName,
        sys: sys
      }, '*');
    } else {
      console.error('SupportSDK: iframe或contentWindow不存在');
    }
  };

  // 添加消息监听器，处理iframe发送的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('SupportSDK: 收到消息', event.data);

      // 处理页面跳转消息
      if (event.data && event.data.type === 'button_change_url') {
        const url = event.data.url;
        console.log('SupportSDK: 打开URL', url);
        window.open(url, '_blank');
      }
    };

    // 添加事件监听
    window.addEventListener('message', handleMessage);
    console.log('SupportSDK: 已添加消息监听器');

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('message', handleMessage);
      console.log('SupportSDK: 已移除消息监听器');
    };
  }, []);

  return (
    <div className={styles.supportSdkContainer}>
      <iframe
        id="button" // 使用文档要求的id
        className={styles.supportSdkIframe}
        src={apiUrl}
        frameBorder="0"
        onLoad={handleIframeLoad}
        height="330"
        width="210"
      />
    </div>
  );
};

export default SupportSDK;
