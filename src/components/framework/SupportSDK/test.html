<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题支撑SDK测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .url-list {
            list-style: none;
            padding: 0;
        }
        .url-list li {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            word-break: break-all;
        }
        .iframe-container {
            position: fixed;
            right: 20px;
            bottom: 100px;
            z-index: 1000;
            width: 210px;
            height: 330px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: white;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 6px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>问题支撑SDK测试页面</h1>
        
        <div class="test-section">
            <h2>测试URL列表</h2>
            <ul class="url-list">
                <li><strong>天宫资源池:</strong> https://172.24.123.29:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0</li>
                <li><strong>DCN映射:</strong> https://10.186.1.29:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0</li>
                <li><strong>B网(OSS_VPN):</strong> https://10.160.4.243:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0</li>
                <li><strong>B网(IOT_BSS):</strong> https://172.20.43.27/api/middlleplatform/mTkrm9/zqBxN8/v1.0</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>测试控制</h2>
            <div class="controls">
                <button onclick="testUrl('https://172.24.123.29:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0')">测试天宫资源池</button>
                <button onclick="testUrl('https://10.186.1.29:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0')">测试DCN映射</button>
                <button onclick="testUrl('https://10.160.4.243:2443/api/middlleplatform/mTkrm9/zqBxN8/v1.0')">测试B网OSS</button>
                <button onclick="testUrl('https://172.20.43.27/api/middlleplatform/mTkrm9/zqBxN8/v1.0')">测试B网IOT</button>
                <button onclick="sendUserInfo()">发送用户信息</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
        </div>

        <div class="test-section">
            <h2>消息日志</h2>
            <div id="log" class="log"></div>
        </div>
    </div>

    <!-- 悬浮窗iframe -->
    <div class="iframe-container">
        <iframe id="button" src="about:blank"></iframe>
    </div>

    <script>
        // 设置测试用户信息
        localStorage.setItem("userName", "testuser");
        localStorage.setItem("sys", "mTkrm9/zqBxN8");

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testUrl(url) {
            log(`测试URL: ${url}`);
            const iframe = document.getElementById('button');
            iframe.src = url;
        }

        function sendUserInfo() {
            const userName = localStorage.getItem("userName");
            const sys = localStorage.getItem("sys");
            const iframe = document.getElementById('button');
            
            log(`发送用户信息: userName=${userName}, sys=${sys}`);
            
            if (iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: "sendUserName",
                    userName: userName,
                    sys: sys
                }, '*');
                log('用户信息已发送');
            } else {
                log('错误: iframe.contentWindow 不存在');
            }
        }

        // iframe加载事件
        document.getElementById('button').addEventListener('load', function() {
            log('iframe 加载完成');
            // 自动发送用户信息
            setTimeout(sendUserInfo, 1000);
        });

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            log(`收到消息: ${JSON.stringify(event.data)}`);
            
            if (event.data && event.data.type === 'button_change_url') {
                const url = event.data.url;
                log(`准备打开URL: ${url}`);
                window.open(url, '_blank');
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('测试页面加载完成');
            log('localStorage中的用户信息:');
            log(`- userName: ${localStorage.getItem("userName")}`);
            log(`- sys: ${localStorage.getItem("sys")}`);
        });
    </script>
</body>
</html>
