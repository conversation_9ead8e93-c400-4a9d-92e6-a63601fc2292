import { addWebLog, loginLastHistory } from '@/api/framework';
import Icon from '@/components/framework/Icon';
import MultiTab from '@/components/framework/MultiTab';
import RightContent from '@/components/framework/RightContent';
import Notify from '@/components/framework/Notify';
import SupportSDK from '@/components/framework/SupportSDK';
import type { ExpandRoute } from '@/utils/framework/RouteUtils';
import RouteUtils from '@/utils/framework/RouteUtils';
import type {
  BasicLayoutProps as ProLayoutProps,
  MenuDataItem,
  Settings,
} from '@ant-design/pro-layout';
import moment from 'moment';
import ProLayout from '@ant-design/pro-layout';
import React, { useEffect, useState } from 'react';
import { KeepAlive as ReactKeepAlive, useAliveController } from 'react-activation';
import { history, Link, useModel } from 'umi';
// import './index.less';

export type BasicLayoutProps = {
  breadcrumbNameMap: Record<string, MenuDataItem>;
  route: ProLayoutProps['route'] & {
    authority: string[];
    routes: any[];
  };
  settings: Settings;
} & ProLayoutProps;

const renderMenuItem = (name: string, hasSub: boolean, icon?: string) => {
  return (
    <span className="ant-pro-menu-item" title={name}>
      {!icon ? <></> : <Icon type={icon} />}
      <span className="ant-pro-menu-item-title">{name}</span>
    </span>
  );
};

const BasicLayout: React.FC<BasicLayoutProps> = (props) => {
  const {
    children,
    location = {
      pathname: '/',
    },
    route,
  } = props;

  const cleanCache: any = () => {};
  const { clear } = useAliveController();
  cleanCache(clear);
  const { routeArray, firstPath, load, setLoad } = useModel('dynamic-route');
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [collapsed, setCollapsed] = useState(false);
  const [keepAliveProps, setKeepAliveProps] = useState<{
    id?: string;
    name?: string;
    isTabs?: boolean;
  }>({});
  const { isLogin, signOut } = useModel('isLogin');

  useEffect(() => {
    if (isLogin) {
      const username = currentUser?.user?.username;
      try {
        loginLastHistory({ username: username }).then((res) => {
          if (res.code === 2000) {
            if (res.data && res.data instanceof Array && res.data.length !== 0)
              Notify.openNotification(username, '系统消息', res.data[0]);
          }
        });
      } catch (error) {
        console.error('日志记录失败' + error);
      }
      signOut();
    } else {
      Notify.clsoeNotification();
    }
  }, [isLogin, signOut, currentUser]);
  useEffect(() => {
    if (location.pathname && location.pathname !== '/') {
      const currenMenu = RouteUtils.getMenuDict()[location.pathname];
      const newKeepAliveProps = {
        id: `${currenMenu?.id}`,
        name: currenMenu?.path,
        isTabs: currenMenu?.isTabs,
      };
      // 404页面处理
      if (!newKeepAliveProps.name) {
        newKeepAliveProps.id = location.pathname;
        newKeepAliveProps.name = location.pathname;
      }
      setKeepAliveProps(newKeepAliveProps);
    }
  }, [location.pathname, route.routes]);

  useEffect(() => {
    if (load) {
      return;
    }
    const newRoute: ExpandRoute = { ...route };
    newRoute.routes = [];
    newRoute.children = [];
    if (routeArray && routeArray.length > 0) {
      for (const element of routeArray) {
        const menu = element;
        newRoute.children.push(menu);
        newRoute.routes.push(menu);
      }

      route.routes = newRoute.routes;
      route.children = newRoute.routes;
      setLoad(true);

      if (location.pathname && location.pathname !== '/') {
        // 解决刷新页面参数丢失问题导致页面功能权限失效问题
        const { query } = location as any;
        history.replace({
          pathname: location.pathname,
          query: query,
        });
      }
    }
  }, [location.pathname, route, routeArray, setLoad, load]);

  if (location.pathname === '/' && firstPath && firstPath !== '/') {
    history.push(firstPath);
  }

  let contentMarginTop = 64;
  if (!initialState?.settings?.fixedHeader) {
    contentMarginTop = 24;
  }
  return (
    <ProLayout
      {...initialState?.settings}
      {...props}
      loading={!load || keepAliveProps.id === undefined}
      route={route}
      collapsed={collapsed}
      onCollapse={setCollapsed}
      contentStyle={{
        marginTop: keepAliveProps?.isTabs ? `${contentMarginTop}px` : undefined,
      }}
      menuProps={{
        onClick: async (e) => {
          const currenMenu = RouteUtils.getMenuDict()[e.key];
          //如果菜单用户菜单存在，则记录日志
          if (currenMenu) {
            const logParams = {
              createdBy: currentUser?.user.realName,
              createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              logContent: '访问' + currenMenu.name,
              //操作日志
              logType: 2,
              //操作类型
              operateType: 0,
              userId: currentUser?.user.id,
              username: currentUser?.user.username,
            };
            try {
              const res = await addWebLog(logParams);
              if (res.code !== 2000) {
                console.error('日志记录失败' + res.msg);
              }
            } catch (error) {
              console.error('日志记录失败' + error);
            }
          }
        },
      }}
      siderWidth={keepAliveProps?.isTabs ? undefined : 0}
      headerHeight={undefined}
      headerRender={(headerProps, defaultDom) => defaultDom}
      rightContentRender={() => <RightContent />}
      onMenuHeaderClick={() => history.push(firstPath || '/')}
      menuItemRender={(menuItemProps) => {
        const { redirectPath, name, icon } = menuItemProps.meta;
        if (!menuItemProps.path || location.pathname === menuItemProps.path) {
          return renderMenuItem(name, false, icon);
        }
        if (menuItemProps.isUrl) {
          return (
            <a target={menuItemProps.target} href={menuItemProps.path}>
              {renderMenuItem(name, false, icon)}
            </a>
          );
        }
        return (
          <Link to={redirectPath || menuItemProps.path}>{renderMenuItem(name, false, icon)}</Link>
        );
      }}
      subMenuItemRender={(itemProps) => {
        const { icon, name } = itemProps;
        return (
          <span className="ant-pro-menu-item" title={name}>
            {!icon ? <></> : <Icon type={icon as string} />}
            <span className="ant-pro-menu-item-title">{name}</span>
          </span>
        );
      }}
    >
      {keepAliveProps?.isTabs && <MultiTab />}

      {keepAliveProps?.isTabs ? (
        <ReactKeepAlive id={keepAliveProps.id} name={keepAliveProps.name}>
          {children}
        </ReactKeepAlive>
      ) : (
        <> {children}</>
      )}

      {/* 问题支撑SDK悬浮窗 */}
      <SupportSDK />
    </ProLayout>
  );
};

export default BasicLayout;
