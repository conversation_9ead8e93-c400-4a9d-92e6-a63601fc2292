var isTianyanCollectData = true;
var staffId = ''; // 业务系统获取工号信息逻辑
var provinceCode = ''; //  业务系统获取省分信息逻辑
if (isTianyanCollectData && isTianyanCollectData) {
  window.uam_xy_paq = window.uam_xy_paq || [];
  window.uam_xy_obj = new Object();
  window.uam_xy_session = new Object();
  (function () {
    window.uam_xy_paq.push(['server_url', 'http://*************:9664/tianyan/sendlog/sendqueue']);
    // 对接系统编码进行修改
    window.uam_xy_paq.push(['systemCode', 'ZYQSMZQXT']);
    // 对接系统licence进行修改
    window.uam_xy_paq.push(['licence', 'ZYQSMZQXT']);
    try {
      window.uam_xy_paq.push(['staffId', staffId || '']);
      window.uam_xy_paq.push(['provinceCode', provinceCode || '']);
    } catch (e) {}
    window.d = document;
    window.g = window.d.createElement('script');
    window.s = window.d.getElementsByTagName('script')[0];
    g.type = 'text/javascript';
    g.async = true;
    g.defer = true;
    g.src = "http://*************:9664/tianyan/uam_public_commonajax.js";
    s.parentNode.insertBefore(g, s);
  })();
}
