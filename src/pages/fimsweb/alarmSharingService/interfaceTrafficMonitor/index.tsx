import { dataExportAllApi, dataGetTotalApi, dataQueryServiceApi } from '@/api/fimsweb/dashboard';
import { ScrollCollapsedY, ScrollProX, ScrollProY } from '@/utils/framework/constant';
import type { ProFormInstance } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Space } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import './index.less';
const InterfaceTrafficMonitor: React.FC = () => {
  const [collapsed, setCollapsed] = useState(true);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [dataColumns, setDataColumns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [querySortSettings, setSortSettings] = useState([]);
  const [varT, setVarT] = useState(0);

  const emunRequest = async (dataId: string, field: string) => {
    const { rows = [] }: any = await dataQueryServiceApi(dataId);
    return rows.map((item: any) => {
      return { label: item[field], value: item[field] };
    });
  };
  useEffect(() => {
    const timer = setTimeout(() => {
      setVarT(varT + 1);
      actionRef.current?.reload();
    }, 6000);
    return () => clearTimeout(timer);
  }, [varT]);
  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 48,
      hideInSearch: true,
      align: 'left',
      key: 'index',
      render: (_, record: any, index: number) => {
        return <Space>{(currentPage - 1) * currentPageSize + index + 1}</Space>;
      },
    },
    {
      title: '数据时间',
      dataIndex: 'day_id',
      key: 'day_id',
      width: 460,
      align: 'center',
      render: (_: any, record: any) => <Space>{record.day_id}</Space>,
      sorter: true,
      hideInSearch: true,
    },

    {
      title: '应用系统名称',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
      request: () => emunRequest('g8vyUMGAeqyo3aBc914', 'app_name'),
      width: 140,
      ellipsis: true,
      sorter: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '集群名称',
      dataIndex: 'kafka_cluster',
      key: 'kafka_cluster',
      width: 120,
      align: 'center',
      hideInSearch: true,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '接口名称',
      dataIndex: 'topic',
      key: 'topic',
      align: 'center',
      width: 120,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '告警量',
      dataIndex: 'data_sum',
      key: 'data_sum',
      align: 'right',
      hideInSearch: true,
      width: 140,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '接口告警总量',
      dataIndex: 'end_offset',
      key: 'end_offset',
      align: 'right',
      hideInSearch: true,
      width: 140,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '流速（条/秒）',
      dataIndex: 'message_opt',
      key: 'message_opt',
      align: 'right',
      hideInSearch: true,
      width: 150,
      ellipsis: true,
      sorter: true,
    },
  ];
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  const onCollapse = () => {
    setCollapsed(!collapsed);
  };
  const requestFunc = async (params: any, sort: any) => {
    const sortSettings: any = [];
    const { pageSize, current } = params;
    for (const [key, val] of Object.entries(sort) as any) {
      sortSettings.push({
        sortField: key,
        sortIndex: dataColumns.findIndex((item: any) => item.field == key),
        sortType: val.slice(0, -3).toLocaleUpperCase(),
      });
    }
    setSortSettings(sortSettings);
    const conditions = [
      {
        attrId: 'app_name',
        attrName: 'app_name',
        valueList: [
          {
            name: params.app_name,
            value: params.app_name,
          },
        ],
      },
      {
        attrId: 'topic',
        attrName: 'topic',
        valueList: [
          {
            name: params.topic,
            value: params.topic,
          },
        ],
      },
    ];
    const { rows = [], columns: cols = [] }: any = await dataQueryServiceApi(
      'fEgTkkybHUDC3n5U4Ig',
      {
        sortSettings: sortSettings,
        pageSetting: {
          pageSize: current == 1 ? 0 : pageSize,
          pageIndex: current - 1,
        },
        conditions: conditions,
      },
    );
    setDataColumns(cols);
    if (current == 1) {
      const totalElements: any = await dataGetTotalApi('fEgTkkybHUDC3n5U4Ig', {
        pageSetting: {
          pageSize: 0,
          pageIndex: 0,
        },
        conditions: conditions,
      });
      setTotal(totalElements);
    }
    return {
      data: rows.slice(0, pageSize),
      success: true,
      total: total,
    };
  };
  const onExport = (formProps: any) => {
    const { app_name, topic } = formProps.form.getFieldsValue();
    setLoading(true);
    dataExportAllApi({
      fileName: '接口服务流量监控详情',
      queryParams: [
        {
          dataId: 'fEgTkkybHUDC3n5U4Ig',
          pageSetting: {},
          sortSettings: querySortSettings,
          conditions: [
            {
              attrId: 'app_name',
              attrName: 'app_name',
              valueList: [
                {
                  name: app_name,
                  value: app_name,
                },
              ],
            },
            {
              attrId: 'topic',
              attrName: 'topic',
              valueList: [
                {
                  name: topic,
                  value: topic,
                },
              ],
            },
          ],
        },
      ],
    }).finally(() => {
      setLoading(false);
    });
  };
  return (
    <div className="interfaceTrafficMonitor">
      <ProTable<any>
        bordered
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        rowKey=""
        size="small"
        search={{
          labelWidth: 'auto',
          //collapsed,
          onCollapse,
          defaultCollapsed: false,
          searchText: '过滤',
          // span: 4,
          optionRender: (searchConfig, formProps, dom) => [
            ...dom,
            <Button
              key="export"
              type="primary"
              onClick={() => onExport(formProps)}
              loading={loading}
            >
              导出
            </Button>,
          ],
        }}
        scroll={{ x: ScrollProX, y: collapsed ? ScrollProY : ScrollCollapsedY }}
        toolbar={{ title: '接口服务流量监控详情' }}
        request={requestFunc}
        pagination={{
          onChange: onShowSizeChange,
          current: currentPage,
          pageSize: currentPageSize,
          showSizeChanger: true,
          total: total,
        }}
      />
    </div>
  );
};
export default InterfaceTrafficMonitor;
