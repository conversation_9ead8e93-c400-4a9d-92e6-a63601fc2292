import { dataExportAllApi, dataGetTotalApi, dataQueryServiceApi } from '@/api/fimsweb/dashboard';
import { ScrollCollapsedY, ScrollProY } from '@/utils/framework/constant';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText, QueryFilter } from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import StatisticCard from '@ant-design/pro-card';
import { Button, Card, Col, Row, Space } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import './index.less';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import type { LineSeriesOption } from 'echarts/charts';
import { LineChart } from 'echarts/charts';
import { Spin } from 'antd';
import type {
  GridComponentOption,
  TitleComponentOption,
  TooltipComponentOption,
  LegendComponentOption,
  DatasetComponentOption,
} from 'echarts/components';
import {
  GridComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  DatasetComponent,
} from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { cloneDeep } from 'lodash';

echarts.use([
  LineChart,
  GridComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  DatasetComponent,
  UniversalTransition,
  CanvasRenderer,
]);
type EChartsOption = echarts.ComposeOption<
  | LineSeriesOption
  | GridComponentOption
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | DatasetComponentOption
>;

const OmcFiveAlarm: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [collapsed, setCollapsed] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);
  const emunRequest = async (dataId: string, field: string) => {
    const { rows = [] }: any = await dataQueryServiceApi(dataId);
    return rows.map((item: any) => {
      return { label: item[field], value: item[field] };
    });
  };
  const defaultOption: EChartsOption = {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const hourNum = params[0]?.data?.hour_num ?? '';
        const loadHour = params[0]?.data?.load_hour ?? '';

        return '每小时告警量<br/>' + loadHour + '：' + hourNum;
        // formatter: '每小时告警量 <br/>{b}：{d}',
      },
    },
    legend: {
      data: [{ name: 'hour_num' }],
      formatter: function () {
        return '每小时告警量';
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      name: '告警量(条)',
      axisLine: {
        show: true,
      },
    },
    dataset: {
      dimensions: ['load_hour', 'hour_num'],
      source: [],
    },
    series: [
      {
        type: 'line',
      },
    ],
  };
  const [chartOption, setChartOpiton] = useState(defaultOption);
  const [chartLoading, setChartLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  const queryData = async (dataId: string, params: any) => {
    return await dataQueryServiceApi(dataId, params);
  };

  const getFilterValues = () => {
    const params = formRef.current?.getFieldsValue() || {};
    return [
      {
        attrId: 'omc',
        attrName: 'omc',
        valueList: [
          {
            name: params.omc,
            value: params.omc,
          },
        ],
      },
      {
        attrId: 'province',
        attrName: 'province',
        valueList: [
          {
            name: params.province,
            value: params.province,
          },
        ],
      },
      {
        attrId: 'specialty',
        attrName: 'specialty',
        valueList: [
          {
            name: params.specialty,
            value: params.specialty,
          },
        ],
      },
      {
        attrId: 'esb_info',
        attrName: 'esb_info',
        valueList: [
          {
            name: params.esb_info,
            value: params.esb_info,
          },
        ],
      },
    ];
  };

  const qeuryChartData = useCallback(async (omc = '') => {
    setChartLoading(true);
    const conditions: any = getFilterValues();
    if (omc != '') {
      conditions[0].valueList = [
        {
          name: omc,
          value: omc,
        },
      ];
    }
    const data: any = await queryData('HwMlyZdl3QctZ95iHF7', {
      pageSetting: {
        pageSize: 0,
        pageIndex: 0,
      },
      conditions: conditions,
    });
    const chartOptionsClone: any = cloneDeep(chartOption);
    chartOptionsClone.dataset.source = data.rows;
    setChartOpiton(chartOptionsClone);
    setChartLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onOmcClick = (omc: any) => {
    qeuryChartData(omc);
  };

  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 48,
      hideInSearch: true,
      align: 'center',
      key: 'index',
      render: (_, record: any, index: number) => {
        return <Space>{(currentPage - 1) * currentPageSize + index + 1}</Space>;
      },
    },
    {
      title: '省份',
      dataIndex: 'province',
      key: 'province',
      hideInSearch: true,
      align: 'center',
      width: 80,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'specialty',
      key: 'specialty',
      hideInSearch: true,
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '网管信息',
      dataIndex: 'esb_info',
      key: 'esb_info',
      hideInSearch: true,
      align: 'center',
      width: 80,
      ellipsis: true,
    },
    {
      title: 'omc',
      dataIndex: 'omc',
      key: 'omc',
      hideInSearch: true,
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (_, record: any) => {
        return (
          <Button type="link" onClick={() => onOmcClick(record.omc)}>
            {record.omc}
          </Button>
        );
      },
    },
    {
      title: '运行状态',
      dataIndex: 'adapter_status',
      key: 'adapter_status',
      hideInSearch: true,
      align: 'left',
      width: 80,
      ellipsis: true,
    },
    {
      title: '中断小时数',
      dataIndex: 'interupt_hours',
      key: 'interupt_hours',
      hideInSearch: true,
      align: 'right',
      width: 90,
    },
    {
      title: '告警阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      hideInSearch: true,
      align: 'right',
      width: 90,
    },
  ];

  const queryTableData = useCallback(async () => {
    setTableLoading(true);
    const conditions = getFilterValues();
    const data: any = await queryData('8Ll6K0l2wTEFRi8LV54', {
      pageSetting: {
        pageSize: currentPageSize,
        pageIndex: currentPage - 1,
      },
      conditions: conditions,
    });
    setTableData(data.rows);
    if (currentPage == 1) {
      const totalElements: any = await dataGetTotalApi('8Ll6K0l2wTEFRi8LV54', {
        pageSetting: {
          pageSize: 0,
          pageIndex: 0,
        },
        conditions: conditions,
      });
      setTotal(totalElements);
    }
    setTableLoading(false);
  }, [currentPage, currentPageSize]);
  const onCollapse = () => {
    setCollapsed(!collapsed);
  };
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  const queryDatas = () => {
    if (currentPage != 1) {
      setCurrentPage(1);
    } else {
      queryTableData();
    }
    qeuryChartData();
  };
  const onExport = () => {
    setExportLoading(true);
    const conditions = getFilterValues();
    dataExportAllApi({
      fileName: 'OMC5分钟告警量',
      queryParams: [
        {
          dataId: '8Ll6K0l2wTEFRi8LV54',
          pageSetting: {},
          sortSettings: [],
          conditions: conditions,
        },
        {
          dataId: 'HwMlyZdl3QctZ95iHF7',
          pageSetting: {},
          sortSettings: [],
          conditions: conditions,
        },
      ],
    }).finally(() => {
      setExportLoading(false);
    });
  };

  useEffect(() => {
    queryTableData();
  }, [queryTableData]);

  useEffect(() => {
    qeuryChartData();
  }, [qeuryChartData]);

  return (
    <>
      <Spin spinning={exportLoading}>
        <div style={{ height: '100%', overflow: 'hidden auto' }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card bodyStyle={{ paddingBottom: 0 }} className="formCard">
                <QueryFilter
                  formRef={formRef}
                  optionRender={(searchConfig, props, dom) => [
                    ...dom,
                    <Button key="export" type="primary" onClick={onExport}>
                      导出
                    </Button>,
                  ]}
                  onCollapse={onCollapse}
                  defaultCollapsed={false}
                  onFinish={async () => {
                    queryDatas();
                  }}
                >
                  <ProFormSelect
                    request={async () => {
                      const res = await emunRequest('8gCHVWMybZfA67P7sE0', 'province');
                      return res;
                    }}
                    name="province"
                    label="省份"
                    showSearch
                  />
                  <ProFormText name="omc" label="omc" />
                  <ProFormSelect
                    request={async () => {
                      const res = await emunRequest('vrRgiAbWGBV011N3MdL', 'specialty');
                      return res;
                    }}
                    name="specialty"
                    label="专业"
                    showSearch
                  />
                  <ProFormText name="esb_info" label="网管信息" />
                </QueryFilter>
              </Card>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <ProTable<any>
                bordered
                size="small"
                tableClassName="proTableCus"
                rowKey="omc"
                columns={columns}
                dataSource={tableData}
                options={{
                  setting: true,
                  density: true,
                  reload: () => {
                    queryTableData();
                  },
                }}
                search={false}
                scroll={{ x: 'max-content', y: collapsed ? ScrollProY : ScrollCollapsedY }}
                pagination={{
                  onChange: onShowSizeChange,
                  current: currentPage,
                  pageSize: currentPageSize,
                  showSizeChanger: true,
                  total: total,
                }}
                loading={tableLoading}
              />
            </Col>
            <Col xs={24} sm={24} md={12}>
              <StatisticCard title="告警量趋势图" style={{ height: '100%' }} loading={chartLoading}>
                <ReactEChartsCore
                  echarts={echarts}
                  option={chartOption}
                  style={{ height: '100%' }}
                  notMerge={true}
                  lazyUpdate={true}
                />
              </StatisticCard>
            </Col>
          </Row>
        </div>
      </Spin>
    </>
  );
};

export default OmcFiveAlarm;
