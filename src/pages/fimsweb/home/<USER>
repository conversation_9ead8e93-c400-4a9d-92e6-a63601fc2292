import ProCard from '@ant-design/pro-card';
import { Col, Row, Image } from 'antd';
import React from 'react';
import './index.less';

const FimsHome: React.FC = () => {
  return (
    <>
      <div className="fimsHome" style={{ height: '100%' }}>
        <Row style={{ height: '100%' }} gutter={16}>
          <Col span={10} style={{ height: '100%' }}>
            <ProCard title="告警管理" style={{ height: '100%' }}>
              {/* <ul>
                <li>告警安全库管理结果</li>
                <li>理想自定义登录可视化实现</li>
              </ul> */}
              <div className="imgStyle">
                <Image width={500} src={require('../home/<USER>/alarm_manage.png')} />
              </div>
            </ProCard>
          </Col>
          <Col span={14} style={{ height: '100%' }}>
            <Row style={{ height: '100%' }}>
              <Col span={24} style={{ height: '50%', paddingBottom: '8px' }}>
                <ProCard style={{ height: '100%' }} title="配置管理">
                  <div className="imgStyle">
                    <Image width={300} src={require('../home/<USER>/configuration_manage.png')} />
                  </div>
                </ProCard>
              </Col>
              <Col span={24} style={{ height: '50%', paddingTop: '8px' }}>
                <Row style={{ height: '100%' }} gutter={16}>
                  <Col span={12} style={{ height: '100%' }}>
                    <ProCard title="运维管理" style={{ height: '100%' }}>
                      <div className="imgStyle">
                        <Image width={250} src={require('../home/<USER>/operation_manage.png')} />
                      </div>
                    </ProCard>
                  </Col>
                  <Col span={12} style={{ height: '100%' }}>
                    <ProCard title="大屏呈现" style={{ height: '100%' }}>
                      <div className="imgStyle">
                        <Image width={250} src={require('../home/<USER>/large_screen.png')} />
                      </div>
                    </ProCard>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default FimsHome;
