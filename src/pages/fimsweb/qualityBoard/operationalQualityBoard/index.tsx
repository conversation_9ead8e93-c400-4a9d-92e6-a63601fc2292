import { dataQueryServiceApi } from '@/api/fimsweb/dashboard';
import { ScrollCollapsedY, ScrollProY } from '@/utils/framework/constant';
import type { ProFormInstance } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProFormSelect, QueryFilter, ProFormDateRangePicker } from '@ant-design/pro-form';

import ProTable from '@ant-design/pro-table';
import { Button, Card, Col, Row } from 'antd';
import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Spin } from 'antd';
import moment from 'moment';

import { dataGetBoardDataApi, dataExportBoardDataApi } from '@/api/fimsweb/alarmWebService';
import './index.less';

const OperationalQualityBoard: React.FC = () => {
  const [collapsed, setCollapsed] = useState(true);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);

  const emunRequest = async (dataId: string, field: string) => {
    const { rows = [] }: any = await dataQueryServiceApi(dataId);
    return rows.map((item: any) => {
      return { label: item[field], value: item[field] };
    });
  };
  const getRealRateVal = (val: any) => {
    if (val != '-') {
      const intVal = parseInt(val.replace('%', ''));
      if (intVal < 98) {
        return <span className="threshold_red">{val}</span>;
      } else {
        return val;
      }
    } else {
      return val;
    }
  };
  const columns: ProColumns<any>[] = [
    // {
    //   title: '序号',
    //   dataIndex: 'index',
    //   width: 48,
    //   hideInSearch: true,
    //   align: 'left',
    //   key: 'index',
    //   render: (_, record: any, index: number) => {
    //     return <Space>{(currentPage - 1) * currentPageSize + index + 1}</Space>;
    //   },
    // },
    {
      title: '省份',
      dataIndex: 'province',
      key: 'province',
      width: 120,
      sorter: false,
      request: () => emunRequest('4Iocel1khF3sf048a1d', 'province'),
      hideInSearch: false,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '子产品名称',
      dataIndex: 'fault_center',
      key: 'fault_center',
      hideInSearch: true,
      width: 110,
      ellipsis: true,
      sorter: false,
    },
    {
      title: '产品运营质量字段与指标',
      children: [
        {
          title: '3G无线专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'threeg_related_rate_statistics',
              key: 'threeg_related_rate_statistics',
              hideInSearch: true,
              //ellipsis: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'threeg_resource_related_rate',
              key: 'threeg_resource_related_rate',
              hideInSearch: true,
              // ellipsis: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'threeg_city',
              key: 'threeg_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'threeg_county',
              key: 'threeg_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'threeg_ne_name',
              key: 'threeg_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'threeg_device_type',
              key: 'threeg_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'threeg_alarm_title',
              key: 'threeg_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'threeg_room',
              key: 'threeg_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.threeg_room);
              },
            },
          ],
        },
        {
          title: '4G无线专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'fourg_related_rate_statistics',
              key: 'fourg_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'fourg_resource_related_rate',
              key: 'fourg_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'fourg_city',
              key: 'fourg_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'fourg_county',
              key: 'fourg_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'fourg_ne_name',
              key: 'fourg_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'fourg_device_type',
              key: 'fourg_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'fourg_alarm_title',
              key: 'fourg_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'fourg_room',
              key: 'fourg_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fourg_room);
              },
            },
          ],
        },
        {
          title: '5G无线专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'fiveg_related_rate_statistics',
              key: 'fiveg_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'fiveg_resource_related_rate',
              key: 'fiveg_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'fiveg_city',
              key: 'fiveg_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'fiveg_county',
              key: 'fiveg_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'fiveg_ne_name',
              key: 'fiveg_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'fiveg_device_type',
              key: 'fiveg_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'fiveg_alarm_title',
              key: 'fiveg_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'fiveg_room',
              key: 'fiveg_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fiveg_room);
              },
            },
          ],
        },
        {
          title: '传输专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'trans_related_rate_statistics',
              key: 'trans_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'trans_resource_related_rate',
              key: 'trans_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'trans_city',
              key: 'trans_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'trans_county',
              key: 'trans_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'trans_ne_name',
              key: 'trans_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'trans_device_type',
              key: 'trans_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'trans_alarm_title',
              key: 'trans_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'trans_room',
              key: 'trans_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.trans_room);
              },
            },
          ],
        },
        {
          title: 'IPRAN专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'ipran_related_rate_statistics',
              key: 'ipran_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'ipran_resource_related_rate',
              key: 'ipran_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'ipran_city',
              key: 'ipran_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'ipran_county',
              key: 'ipran_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'ipran_ne_name',
              key: 'ipran_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'ipran_device_type',
              key: 'ipran_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'ipran_alarm_title',
              key: 'ipran_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'ipran_room',
              key: 'ipran_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.ipran_room);
              },
            },
          ],
        },
        {
          title: '数据专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'data_related_rate_statistics',
              key: 'data_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'data_resource_related_rate',
              key: 'data_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.data_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'data_city',
              key: 'data_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.data_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'data_county',
              key: 'data_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.data_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'data_ne_name',
              key: 'data_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.data_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'data_device_type',
              key: 'data_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.data_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'data_alarm_title',
              key: 'data_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.data_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'data_room',
              key: 'data_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.data_room);
              },
            },
          ],
        },
        {
          title: '智能城域网专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'smart_city_related_rate_statistics',
              key: 'smart_city_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'smart_city_resource_related_rate',
              key: 'smart_city_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'smart_city_city',
              key: 'smart_city_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_city);
              },
            },
            {
              title: '所属区县',
              dataIndex: 'smart_city_county',
              key: 'smart_city_county',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_county);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'smart_city_ne_name',
              key: 'smart_city_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'smart_city_device_type',
              key: 'smart_city_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'smart_city_alarm_title',
              key: 'smart_city_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_alarm_title);
              },
            },
            {
              title: '所属机房',
              dataIndex: 'smart_city_room',
              key: 'smart_city_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.smart_city_room);
              },
            },
          ],
        },
        {
          title: '核心专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'core_related_rate_statistics',
              key: 'core_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'core_resource_related_rate',
              key: 'core_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.core_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'core_city',
              key: 'core_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.core_city);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'core_ne_name',
              key: 'core_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.core_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'core_device_type',
              key: 'core_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.core_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'core_alarm_title',
              key: 'core_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.core_alarm_title);
              },
            },
            {
              title: '设备SDN',
              dataIndex: 'core_device_sdn',
              key: 'core_device_sdn',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.core_device_sdn);
              },
            },
          ],
        },
        {
          title: '固网专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'fixed_related_rate_statistics',
              key: 'fixed_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'fixed_resource_related_rate',
              key: 'fixed_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.fixed_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'fixed_city',
              key: 'fixed_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fixed_city);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'fixed_ne_name',
              key: 'fixed_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fixed_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'fixed_device_type',
              key: 'fixed_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fixed_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'fixed_alarm_title',
              key: 'fixed_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fixed_alarm_title);
              },
            },
            {
              title: '设备SDN',
              dataIndex: 'fixed_device_sdn',
              key: 'fixed_device_sdn',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.fixed_device_sdn);
              },
            },
          ],
        },
        {
          title: '省VIMS专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'province_vims_related_rate_statistics',
              key: 'province_vims_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'province_vims_resource_related_rate',
              key: 'province_vims_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.province_vims_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'province_vims_city',
              key: 'province_vims_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.province_vims_city);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'province_vims_ne_name',
              key: 'province_vims_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.province_vims_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'province_vims_device_type',
              key: 'province_vims_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.province_vims_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'province_vims_alarm_title',
              key: 'province_vims_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.province_vims_alarm_title);
              },
            },
            {
              title: '设备SDN',
              dataIndex: 'province_vims_device_sdn',
              key: 'province_vims_device_sdn',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.province_vims_device_sdn);
              },
            },
          ],
        },
        {
          title: '大区VIMS专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'region_vims_related_rate_statistics',
              key: 'region_vims_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'region_vims_resource_related_rate',
              key: 'region_vims_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
              render: (_, record: any) => {
                return getRealRateVal(record.region_vims_resource_related_rate);
              },
            },
            {
              title: '所属地市',
              dataIndex: 'region_vims_city',
              key: 'region_vims_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.region_vims_city);
              },
            },
            {
              title: '网元名称',
              dataIndex: 'region_vims_ne_name',
              key: 'region_vims_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.region_vims_ne_name);
              },
            },
            {
              title: '设备类型',
              dataIndex: 'region_vims_device_type',
              key: 'region_vims_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.region_vims_device_type);
              },
            },
            {
              title: '告警标题',
              dataIndex: 'region_vims_alarm_title',
              key: 'region_vims_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.region_vims_alarm_title);
              },
            },
            {
              title: '设备SDN',
              dataIndex: 'region_vims_device_sdn',
              key: 'region_vims_device_sdn',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
              render: (_, record: any) => {
                return getRealRateVal(record.region_vims_device_sdn);
              },
            },
          ],
        },
        {
          title: '5GC专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'fivegc_related_rate_statistics',
              key: 'fivegc_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'fivegc_resource_related_rate',
              key: 'fivegc_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
            },
            {
              title: '所属地市',
              dataIndex: 'fivegc_city',
              key: 'fivegc_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '网元名称',
              dataIndex: 'fivegc_ne_name',
              key: 'fivegc_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '设备类型',
              dataIndex: 'fivegc_device_type',
              key: 'fivegc_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '告警标题',
              dataIndex: 'fivegc_alarm_title',
              key: 'fivegc_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '设备SDN',
              dataIndex: 'fivegc_device_sdn',
              key: 'fivegc_device_sdn',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
          ],
        },
        {
          title: '动环专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'dynamic_environment_related_rate_statistics',
              key: 'dynamic_environment_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'dynamic_environment_resource_related_rate',
              key: 'dynamic_environment_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
            },
            {
              title: '所属地市',
              dataIndex: 'dynamic_environment_city',
              key: 'dynamic_environment_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '所属区县',
              dataIndex: 'dynamic_environment_district',
              key: 'dynamic_environment_district',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '网元名称',
              dataIndex: 'dynamic_environment_ne_name',
              key: 'dynamic_environment_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '设备类型',
              dataIndex: 'dynamic_environment_device_type',
              key: 'dynamic_environment_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '告警标题',
              dataIndex: 'dynamic_environment_alarm_title',
              key: 'dynamic_environment_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '机房',
              dataIndex: 'dynamic_environment_room',
              key: 'dynamic_environment_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
          ],
        },
        {
          title: '铁塔专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'tower_related_rate_statistics',
              key: 'tower_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'tower_resource_related_rate',
              key: 'tower_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
            },
            {
              title: '所属地市',
              dataIndex: 'tower_city',
              key: 'tower_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '所属区县',
              dataIndex: 'tower_district',
              key: 'tower_district',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '网元名称',
              dataIndex: 'tower_ne_name',
              key: 'tower_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '设备类型',
              dataIndex: 'tower_device_type',
              key: 'tower_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '告警标题',
              dataIndex: 'tower_alarm_title',
              key: 'tower_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '机房',
              dataIndex: 'tower_room',
              key: 'tower_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
          ],
        },
        {
          title: '接入专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'access_related_rate_statistics',
              key: 'access_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '资源关联率',
              dataIndex: 'access_resource_related_rate',
              key: 'access_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 90,
            },
            {
              title: '所属地市',
              dataIndex: 'access_city',
              key: 'access_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '所属区县',
              dataIndex: 'access_district',
              key: 'access_district',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '网元名称',
              dataIndex: 'access_ne_name',
              key: 'access_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '设备类型',
              dataIndex: 'access_device_type',
              key: 'access_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '告警标题',
              dataIndex: 'access_alarm_title',
              key: 'access_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '机房',
              dataIndex: 'access_room',
              key: 'access_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
          ],
        },
        {
          title: '增值专业派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'value_added_related_rate_statistics',
              key: 'value_added_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
            },
            {
              title: '所属地市',
              dataIndex: 'value_added_city',
              key: 'value_added_city',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '网元名称',
              dataIndex: 'value_added_ne_name',
              key: 'value_added_ne_name',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '设备类型',
              dataIndex: 'value_added_device_type',
              key: 'value_added_device_type',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
            {
              title: '告警标题',
              dataIndex: 'value_added_alarm_title',
              key: 'value_added_alarm_title',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 80,
            },
          ],
        },
        {
          title: '骨干HSTP派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_hstp_related_rate_statistics',
              key: 'backbone_hstp_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_hstp_resource_related_rate',
              key: 'backbone_hstp_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_hstp_room',
              key: 'backbone_hstp_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干DRA派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_dra_related_rate_statistics',
              key: 'backbone_dra_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_dra_resource_related_rate',
              key: 'backbone_dra_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_dra_room',
              key: 'backbone_dra_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干CMN派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_cmn_related_rate_statistics',
              key: 'backbone_cmn_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_cmn_resource_related_rate',
              key: 'backbone_cmn_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_cmn_room',
              key: 'backbone_cmn_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '大区NB物联网派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'region_nb_iot_related_rate_statistics',
              key: 'region_nb_iot_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'region_nb_iot_resource_related_rate',
              key: 'region_nb_iot_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'region_nb_iot_room',
              key: 'region_nb_iot_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干169派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_169_related_rate_statistics',
              key: 'backbone_169_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_169_resource_related_rate',
              key: 'backbone_169_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_169_room',
              key: 'backbone_169_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干承载A网派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_carryingA_related_rate_statistics',
              key: 'backbone_carryingA_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_carryingA_resource_related_rate',
              key: 'backbone_carryingA_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_carryingA_room',
              key: 'backbone_carryingA_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干承载B网派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_carryingB_related_rate_statistics',
              key: 'backbone_carryingB_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_carryingB_resource_related_rate',
              key: 'backbone_carryingB_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_carryingB_room',
              key: 'backbone_carryingB_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干SDH派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_sdh_related_rate_statistics',
              key: 'backbone_sdh_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_sdh_resource_related_rate',
              key: 'backbone_sdh_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_sdh_room',
              key: 'backbone_sdh_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干WDM派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_wdm_related_rate_statistics',
              key: 'backbone_wdm_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_wdm_resource_related_rate',
              key: 'backbone_wdm_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_wdm_room',
              key: 'backbone_wdm_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干OTN派单字段补全率（达标要求98%）',
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_otn_related_rate_statistics',
              key: 'backbone_otn_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_otn_resource_related_rate',
              key: 'backbone_otn_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_otn_room',
              key: 'backbone_otn_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干ASON派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_ason_related_rate_statistics',
              key: 'backbone_ason_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_ason_resource_related_rate',
              key: 'backbone_ason_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_ason_room',
              key: 'backbone_ason_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: '骨干ROADM派单字段补全率（达标要求98%）',
          width: 320,
          children: [
            {
              title: '关联率统计值',
              dataIndex: 'backbone_roadm_related_rate_statistics',
              key: 'backbone_roadm_related_rate_statistics',
              hideInSearch: true,
              sorter: false,
              width: 120,
            },
            {
              title: '资源关联率',
              dataIndex: 'backbone_roadm_resource_related_rate',
              key: 'backbone_roadm_resource_related_rate',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
            {
              title: '机房',
              dataIndex: 'backbone_roadm_room',
              key: 'backbone_roadm_room',
              hideInSearch: true,
              sorter: false,
              align: 'right',
              width: 100,
            },
          ],
        },
      ],
    },
    {
      title: '子产品名称',
      dataIndex: 'alarmSubscribe',
      key: 'alarmSubscribe',
      hideInSearch: true,
      width: 100,
      ellipsis: true,
      sorter: false,
    },
    {
      title: '产品运营质量字段与指标',
      children: [
        {
          title: '3G无线',
          dataIndex: 'alarm_subscription_3g_wireless',
          key: 'alarm_subscription_3g_wireless',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '4G无线',
          dataIndex: 'alarm_subscription_4g_wireless',
          key: 'alarm_subscription_4g_wireless',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '5G无线',
          dataIndex: 'alarm_subscription_5g_wireless',
          key: 'alarm_subscription_5g_wireless',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '传输网',
          dataIndex: 'alarm_subscription_transport_network',
          key: 'alarm_subscription_transport_network',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: 'IPRAN',
          dataIndex: 'alarm_subscription_ipran',
          key: 'alarm_subscription_ipran',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '数据网',
          dataIndex: 'alarm_subscription_data_network',
          key: 'alarm_subscription_data_network',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '智能城域网',
          dataIndex: 'alarm_subscription_smart_area_net',
          key: 'alarm_subscription_smart_area_net',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 100,
          ellipsis: true,
        },
        {
          title: '核心CS/PS',
          dataIndex: 'alarm_subscription_core_cs_ps',
          key: 'alarm_subscription_core_cs_ps',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 100,
          ellipsis: true,
        },
        {
          title: 'IMS',
          dataIndex: 'alarm_subscription_ims',
          key: 'alarm_subscription_ims',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: 'vIMS',
          dataIndex: 'alarm_subscription_vims',
          key: 'alarm_subscription_vims',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '动环',
          dataIndex: 'alarm_subscription_dynamic_environment',
          key: 'alarm_subscription_dynamic_environment',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '铁塔',
          dataIndex: 'alarm_subscription_tower',
          key: 'alarm_subscription_tower',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '接入网',
          dataIndex: 'alarm_subscription_access_network',
          key: 'alarm_subscription_access_network',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '增值平台',
          dataIndex: 'alarm_subscription_value_added_platform',
          key: 'alarm_subscription_value_added_platform',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
      ],
    },
    {
      title: '子产品名称',
      dataIndex: 'alarmCollection',
      key: 'alarmCollection',
      hideInSearch: true,
      width: 100,
      ellipsis: true,
      sorter: false,
    },
    {
      title: '产品运营质量字段与指标',
      children: [
        {
          title: '3G无线',
          dataIndex: 'alarm_collection_method_3g_wireless',
          key: 'alarm_collection_method_3g_wireless',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '4G无线',
          dataIndex: 'alarm_collection_method_4g_wireless',
          key: 'alarm_collection_method_4g_wireless',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '5G无线',
          dataIndex: 'alarm_collection_method_5g_wireless',
          key: 'alarm_collection_method_5g_wireless',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '传输网',
          dataIndex: 'alarm_collection_method_transport_network',
          key: 'alarm_collection_method_transport_network',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: 'IPRAN',
          dataIndex: 'alarm_collection_method_ipran',
          key: 'alarm_collection_method_ipran',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '数据网',
          dataIndex: 'alarm_collection_method_data_network',
          key: 'alarm_collection_method_data_network',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '智能城域网',
          dataIndex: 'alarm_collection_method_smart_area_net',
          key: 'alarm_collection_method_smart_area_net',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 100,
          ellipsis: true,
        },
        {
          title: '核心CS/PS',
          dataIndex: 'alarm_collection_method_core_cs_ps',
          key: 'alarm_collection_method_core_cs_ps',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 100,
          ellipsis: true,
        },
        {
          title: 'IMS',
          dataIndex: 'alarm_collection_method_ims',
          key: 'alarm_collection_method_ims',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: 'vIMS',
          dataIndex: 'alarm_collection_method_vims',
          key: 'alarm_collection_method_vims',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '动环',
          dataIndex: 'alarm_collection_method_dynamic_environment',
          key: 'alarm_collection_method_dynamic_environment',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '铁塔',
          dataIndex: 'alarm_collection_method_tower',
          key: 'alarm_collection_method_tower',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '接入网',
          dataIndex: 'alarm_collection_method_access_network',
          key: 'alarm_collection_method_access_network',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
        {
          title: '增值平台',
          dataIndex: 'alarm_collection_method_value_added_platform',
          key: 'alarm_collection_method_value_added_platform',
          hideInSearch: true,
          sorter: false,
          align: 'center',
          width: 80,
          ellipsis: true,
        },
      ],
    },
  ];
  const queryTableData = useCallback(async () => {
    setTableLoading(true);
    const params = formRef.current?.getFieldsValue() || {};

    params.startTime = moment(params?.loadTime?.[0] ?? '').format('YYYY-MM-DD');
    params.endTime = moment(params?.loadTime?.[1] ?? '').format('YYYY-MM-DD');
    const paramsNew = {
      startTime: params?.startTime ?? '',
      endTime: params?.endTime ?? '',
      pageIndex: currentPage,
      pageSize: currentPageSize,
      provinceName: params?.province ?? '',
    };
    const { list = [], totalCount }: any = await dataGetBoardDataApi(paramsNew);
    setTableData(list);
    setTotal(totalCount);
    setTableLoading(false);
  }, [currentPage, currentPageSize]);

  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  const onCollapse = () => {
    setCollapsed(!collapsed);
  };
  const queryDatas = () => {
    if (currentPage != 1) {
      setCurrentPage(1);
    } else {
      queryTableData();
    }
  };

  useEffect(() => {
    queryTableData();
  }, [queryTableData]);

  const onExport = async () => {
    const params = formRef.current?.getFieldsValue() || {};
    // const { loadTime, province } = formProps.form.getFieldsValue();
    const paramsNew = {
      startTime: moment(params?.loadTime?.[0] ?? '').format('YYYY-MM-DD'),
      endTime: moment(params?.loadTime?.[1] ?? '').format('YYYY-MM-DD'),
      provinceName: params?.province ?? '',
    };
    setLoading(true);
    await dataExportBoardDataApi(paramsNew);
    setLoading(false);
  };

  return (
    <div className="operationalQualityBoard">
      <Spin spinning={loading}>
        <div style={{ height: '100%', overflow: 'hidden auto' }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card bodyStyle={{ paddingBottom: 0 }} className="formCard">
                <QueryFilter
                  formRef={formRef}
                  optionRender={(searchConfig, props, dom) => [
                    ...dom,
                    <Button key="export" type="primary" onClick={onExport}>
                      导出
                    </Button>,
                  ]}
                  onCollapse={onCollapse}
                  defaultCollapsed={false}
                  onFinish={async () => {
                    queryDatas();
                  }}
                >
                  <ProFormDateRangePicker
                    label="数据时间"
                    dataFormat="YYYY-MM-DD"
                    name="loadTime"
                    initialValue={[
                      moment().add(-7, 'days').format('YYYY-MM-DD'),
                      moment().format('YYYY-MM-DD'),
                    ]}
                  />
                  <ProFormSelect
                    request={async () => {
                      const res = await emunRequest('8gCHVWMybZfA67P7sE0', 'province');
                      return res;
                    }}
                    name="province"
                    label="省份"
                    showSearch
                  />
                </QueryFilter>
              </Card>
            </Col>
            <Col xs={24} sm={24} md={24}>
              <ProTable<any>
                size="small"
                bordered
                tableClassName="proTableCus"
                columns={columns}
                actionRef={actionRef}
                formRef={formRef}
                rowKey="omc"
                dataSource={tableData}
                options={{
                  setting: true,
                  density: true,
                  reload: () => {
                    queryTableData();
                  },
                }}
                search={false}
                scroll={{ x: 'max-content', y: collapsed ? ScrollProY : ScrollCollapsedY }}
                pagination={{
                  onChange: onShowSizeChange,
                  current: currentPage,
                  pageSize: currentPageSize,
                  showSizeChanger: true,
                  total: total,
                }}
                loading={tableLoading}
              />
            </Col>
          </Row>
        </div>
      </Spin>
    </div>
  );
};

export default OperationalQualityBoard;
