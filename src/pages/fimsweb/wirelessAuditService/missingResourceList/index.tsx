import { dataExportAllApi, dataGetTotalApi, dataQueryServiceApi } from '@/api/fimsweb/dashboard';
import { ScrollCollapsedY, ScrollProX, ScrollProY } from '@/utils/framework/constant';
import type { ProFormInstance } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Space } from 'antd';
import moment from 'moment';
import React, { useRef, useState, useEffect } from 'react';
import type { Location } from 'umi';
import { useHistory } from 'umi';
import './index.less';

const MissingResourceList: React.FC = () => {
  const history = useHistory();
  const { location } = history;
  const [collapsed, setCollapsed] = useState(true);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [dataColumns, setDataColumns] = useState([]);
  const [queryDate, setQueryDate] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [loading, setLoading] = useState(false);
  const [querySortSettings, setSortSettings] = useState([]);
  // const { location } = useHistory();
  // const { pathname = '', query = {} } = location as Location;
  // const [netManagerIp, setNetManageIp] = useState(query.netManagerIp);

  const emunRequest = async (dataId: string, field: string) => {
    const { rows = [] }: any = await dataQueryServiceApi(dataId);
    return rows.map((item: any) => {
      return { label: item[field], value: item[field] };
    });
  };
  const staticOptionNetworkType = async () => {
    return [
      { label: '全部', value: '' },
      { label: '4G', value: '104' },
      { label: '5G', value: '105' },
    ];
  };
  const routeRequest = (query: any) => {
    if (query.netManagerIp) {
      formRef.current?.setFieldsValue({
        ip: query.netManagerIp,
      });
      formRef.current?.submit();
    }
  };
  const url = '/alarmManage/wirelessAuditService/missingResourceList';
  useEffect(() => {
    // 添加路由监听函数
    const unlisten = history.listen((historyLocation: Location) => {
      const { query = {}, pathname = '' } = historyLocation;
      if (pathname != url) return;
      routeRequest(query);
    });
    return () => {
      unlisten();
    };
  }, [history, location]);

  useEffect(() => {
    const { query, pathname = '' }: any = location;
    if (pathname != url) return;
    routeRequest(query);
  }, [location]);

  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 48,
      hideInSearch: true,
      align: 'left',
      key: 'index',
      render: (_, record: any, index: number) => {
        return <Space>{(currentPage - 1) * currentPageSize + index + 1}</Space>;
      },
    },
    {
      title: '时间',
      dataIndex: 'day_id',
      key: 'day_id',
      width: 160,
      valueType: 'dateRange',
      render: (_: any, record: any) => <Space>{record.day_id}</Space>,
      initialValue: [moment().add(-7, 'days').format('YYYYMMDD'), moment().format('YYYYMMDD')],
      sorter: true,
    },
    {
      title: '省份',
      dataIndex: 'province',
      key: 'province',
      align: 'left',
      request: () => emunRequest('8AbcsZsW5cTvhg7XPnC', 'province'),
      width: 100,
      ellipsis: true,
      sorter: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '网管IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '网络类型',
      dataIndex: 'networkType',
      key: 'networkType',
      align: 'left',
      width: 120,
      request: () => staticOptionNetworkType(),
      ellipsis: true,
      sorter: true,
    },
    {
      title: '厂家名称',
      dataIndex: 'vendor',
      key: 'vendor',
      align: 'left',
      width: 120,
      ellipsis: true,
      request: () => emunRequest('u0xGEvJHl158BePI68x', 'vendor'),
      sorter: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '设备类型',
      dataIndex: 'equipment_class_txt',
      key: 'equipment_class_txt',
      align: 'left',
      request: () => emunRequest('YBHwO4itzsvYBvkmdJ4', 'equipment_class_txt'),
      width: 120,
      ellipsis: true,
      sorter: true,
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '网元名称',
      dataIndex: 'equipment_name',
      key: 'equipment_name',
      align: 'left',
      hideInSearch: false,
      width: 140,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '采集前置标识',
      dataIndex: 'equipment_dn',
      key: 'equipment_dn',
      align: 'left',
      hideInSearch: true,
      width: 140,
      ellipsis: true,
      sorter: true,
    },
  ];
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  const onCollapse = () => {
    setCollapsed(!collapsed);
  };
  const requestFunc = async (params: any, sort: any) => {
    const sortSettings: any = [];
    const { pageSize, current } = params;
    for (const [key, val] of Object.entries(sort) as any) {
      sortSettings.push({
        sortField: key,
        sortIndex: dataColumns.findIndex((item: any) => item.field == key),
        sortType: val.slice(0, -3).toLocaleUpperCase(),
      });
    }
    setSortSettings(sortSettings);
    const conditions = [
      {
        attrId: 'day_id',
        attrName: 'day_id',
        valueList: [
          {
            name: params.day_id
              ? moment(params?.day_id?.[0] ?? '').format('YYYYMMDD')
              : moment().add(-7, 'days').format('YYYYMMDD'),
            value: params.day_id
              ? moment(params?.day_id?.[0] ?? '').format('YYYYMMDD')
              : moment().add(-7, 'days').format('YYYYMMDD'),
          },
          {
            name: params.day_id
              ? moment(params?.day_id?.[1] ?? '').format('YYYYMMDD')
              : moment().format('YYYYMMDD'),
            value: params.day_id
              ? moment(params?.day_id?.[1] ?? '').format('YYYYMMDD')
              : moment().format('YYYYMMDD'),
          },
        ],
      },
      {
        attrId: 'province',
        attrName: 'province',
        valueList: [
          {
            name: params.province,
            value: params.province,
          },
        ],
      },
      {
        attrId: 'vendor',
        attrName: 'vendor',
        valueList: [
          {
            name: params.vendor,
            value: params.vendor,
          },
        ],
      },
      {
        attrId: 'ip',
        attrName: 'ip',
        valueList: [
          {
            name: params.ip,
            value: params.ip,
          },
        ],
      },
      {
        attrId: 'networkType',
        attrName: 'networkType',
        valueList: [
          {
            name: params.networkType,
            value: params.networkType,
          },
        ],
      },
      {
        attrId: 'equipment_class_txt',
        attrName: 'equipment_class_txt',
        valueList: [
          {
            name: params.equipment_class_txt,
            value: params.equipment_class_txt,
          },
        ],
      },
      {
        attrId: 'equipment_name',
        attrName: 'equipment_name',
        valueList: [
          {
            name: params.equipment_name,
            value: params.equipment_name,
          },
        ],
      },
    ];
    const { rows = [], columns: cols = [] }: any = await dataQueryServiceApi(
      '2TqMN3e9pKDMJI7e0kH',
      {
        sortSettings: sortSettings,
        pageSetting: {
          pageSize: current == 1 ? 0 : pageSize,
          pageIndex: current - 1,
        },
        conditions: conditions,
      },
    );
    setDataColumns(cols);
    if (current == 1) {
      const totalElements: any = await dataGetTotalApi('2TqMN3e9pKDMJI7e0kH', {
        pageSetting: {
          pageSize: 0,
          pageIndex: 0,
        },
        conditions: conditions,
      });
      setTotal(totalElements);
    }
    setQueryDate(moment().format('YYYY-MM-DD HH:mm:ss'));
    return {
      data: rows.slice(0, pageSize),
      success: true,
      total: total,
    };
  };
  const onExport = (formProps: any) => {
    const { day_id, province, vendor, ip, networkType, equipment_name, equipment_class_txt } =
      formProps.form.getFieldsValue();
    setLoading(true);
    dataExportAllApi({
      fileName: '资源缺失清单',
      queryParams: [
        {
          dataId: '2TqMN3e9pKDMJI7e0kH',
          pageSetting: {},
          sortSettings: querySortSettings,
          conditions: [
            {
              attrId: 'day_id',
              attrName: 'day_id',
              valueList: [
                {
                  name: moment(day_id?.[0] ?? '').format('YYYYMMDD'),
                  value: moment(day_id?.[0] ?? '').format('YYYYMMDD'),
                },
                {
                  name: moment(day_id?.[1] ?? '').format('YYYYMMDD'),
                  value: moment(day_id?.[1] ?? '').format('YYYYMMDD'),
                },
              ],
            },
            {
              attrId: 'province',
              attrName: 'province',
              valueList: [
                {
                  name: province,
                  value: province,
                },
              ],
            },
            {
              attrId: 'vendor',
              attrName: 'vendor',
              valueList: [
                {
                  name: vendor,
                  value: vendor,
                },
              ],
            },
            {
              attrId: 'ip',
              attrName: 'ip',
              valueList: [
                {
                  name: ip,
                  value: ip,
                },
              ],
            },
            {
              attrId: 'networkType',
              attrName: 'networkType',
              valueList: [
                {
                  name: networkType,
                  value: networkType,
                },
              ],
            },
            {
              attrId: 'equipment_class_txt',
              attrName: 'equipment_class_txt',
              valueList: [
                {
                  name: equipment_class_txt,
                  value: equipment_class_txt,
                },
              ],
            },
            {
              attrId: 'equipment_name',
              attrName: 'equipment_name',
              valueList: [
                {
                  name: equipment_name,
                  value: equipment_name,
                },
              ],
            },
          ],
        },
      ],
    }).finally(() => {
      setLoading(false);
    });
  };
  return (
    <div className="missingResourceList">
      <ProTable<any>
        bordered
        size="small"
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        rowKey=""
        search={{
          labelWidth: 'auto',
          //collapsed,
          onCollapse,
          defaultCollapsed: false,
          optionRender: (searchConfig, formProps, dom) => [
            ...dom,
            <Button
              key="export"
              type="primary"
              onClick={() => onExport(formProps)}
              loading={loading}
            >
              导出
            </Button>,
          ],
        }}
        scroll={{ x: ScrollProX, y: collapsed ? ScrollProY : ScrollCollapsedY }}
        toolbar={{ title: '资源缺失清单', subTitle: `(数据查询时间：${queryDate})` }}
        request={requestFunc}
        pagination={{
          onChange: onShowSizeChange,
          current: currentPage,
          pageSize: currentPageSize,
          showSizeChanger: true,
          total: total,
        }}
      />
    </div>
  );
};
export default MissingResourceList;
