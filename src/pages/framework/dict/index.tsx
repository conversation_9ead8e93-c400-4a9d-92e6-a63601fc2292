import {
  addDict,
  deleteDict,
  queryDictList,
  setStatus,
  updateDict,
} from '@/pages/framework/dict/service';
import { calcPageNo } from '@/utils/utils';
import { ScrollProX, ScrollProY } from '@/utils/framework/constant';
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  ModalForm,
  ProFormDigit,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Modal, Switch } from 'antd';
import React, { useRef, useState } from 'react';
import { useAccess } from 'umi';

const DictManage: React.FC = () => {
  const access = useAccess();
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [pageTotal, setTotal] = useState<any>();

  const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 14 },
  };
  const actionRef = useRef<ActionType>();
  const handleClick = async (checked: boolean, record: any): Promise<void> => {
    try {
      const response: any = await setStatus({
        id: record.id,
        status: checked,
      });
      if (response.code === 2000) {
        actionRef.current?.reload();
        message.success('更新状态成功！');
      }
    } catch (error) {
      message.success('更新状态失败！');
    }
  };
  const handleDelete = async (id: number): Promise<void> => {
    try {
      const response: any = await deleteDict({ id: id });
      if (response.code === 2000) {
        actionRef.current?.reload();
        message.success('删除字典成功!');
        const pageNo = calcPageNo(pageTotal, currentPage, currentPageSize);
        setCurrentPage(pageNo);
      }
    } catch (error) {
      message.success('删除字典失败！');
    }
  };
  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 48,
      align: 'center',
      editable: false,
      hideInSearch: true,
      render: (_, record: any, index: number) => {
        return <span>{(currentPage - 1) * currentPageSize + index + 1}</span>;
      },
    },
    {
      title: '字典编码',
      dataIndex: 'dictCode',
      key: 'dictCode',
      width: 180,
      align: 'left',
      ellipsis: true,
      editable: false,
    },
    {
      title: '字典名称',
      dataIndex: 'dictName',
      key: 'dictName',
      width: 180,
      align: 'left',
      ellipsis: true,
    },
    {
      title: '字典值',
      dataIndex: 'dictValue',
      key: 'dictValue',
      hideInSearch: true,
      ellipsis: true,
      width: 200,
      align: 'left',
    },
    {
      title: '备注',
      dataIndex: 'description',
      key: 'description',
      hideInSearch: true,
      width: 120,
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      editable: false,
      width: 180,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
      editable: false,
      width: 180,
      align: 'center',
    },
    {
      title: '是否使用',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      align: 'center',
      hideInSearch: true,
      valueType: 'switch',
      fieldProps: {
        checkedChildren: '正常',
        unCheckedChildren: '已停用',
      },
      render: (_, record) => {
        return (
          <Switch
            checkedChildren="正常"
            unCheckedChildren="已停用"
            defaultChecked
            checked={record.status}
            onClick={(checked: boolean) => handleClick(checked, record)}
          />
        );
      },
    },
    {
      title: '操作',
      width: 200,
      valueType: 'option',
      key: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record, _, action) => (
        <>
          <Button
            key="edit"
            type="link"
            icon={<EditOutlined />}
            hidden={!access.hasBtnPerms('dict-edit-button')}
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            key="batchRemove"
            hidden={!access.hasBtnPerms('dict-delete-button')}
            onClick={async () => {
              Modal.confirm({
                title: '删除',
                content: '确定删除该字典项吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  handleDelete(record.id);
                },
              });
            }}
          >
            <DeleteOutlined />
            删除
          </Button>
        </>
      ),
    },
  ];
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  const saveText = (
    <>
      <div style={{ marginLeft: '16px' }}>
        <CheckOutlined style={{ marginRight: '7px' }} />
        保存
      </div>
    </>
  );
  const cancelText = (
    <>
      <div style={{ marginLeft: '18px' }}>
        <CloseOutlined style={{ marginRight: '7px' }} />
        取消
      </div>
    </>
  );
  return (
    <>
      <ProTable
        rowKey="id"
        key="id"
        bordered
        columns={columns}
        actionRef={actionRef}
        scroll={{ x: ScrollProX, y: ScrollProY }}
        pagination={{
          defaultPageSize: 10,
          onChange: onShowSizeChange,
          current: currentPage,
          showSizeChanger: true,
        }}
        request={async (params) => {
          const { data, total } = await queryDictList({
            ...params,
            pageSize: params.pageSize,
            current: currentPage,
            currentPage: currentPage,
          });
          setTotal(total);
          return {
            data,
            success: true,
            total,
          };
        }}
        editable={{
          type: 'multiple',
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
          saveText: saveText,
          cancelText: cancelText,
          onSave: async (key, record, originRow) => {
            try {
              if (record !== originRow) {
                const response: any = await updateDict(record);
                if (response.code === 2000) message.success('更新成功!');
              }
              actionRef.current?.reload();
            } catch (error) {
              console.log(error);
            }
          },
        }}
        toolBarRender={() => [
          <ModalForm
            title="新建"
            trigger={
              <Button type="primary" key="show" hidden={!access.hasBtnPerms('dict-add-button')}>
                <PlusOutlined />
                新建
              </Button>
            }
            autoFocusFirstInput
            modalProps={{
              destroyOnClose: true,
            }}
            {...formItemLayout}
            layout={'horizontal'}
            onFinish={async (values): Promise<boolean> => {
              const response: any = await addDict(values);
              if (response.code === 2000) {
                message.success('新建成功!');
                actionRef.current?.reload();
                return true;
              } else return false;
            }}
            key={'add'}
          >
            <ProFormText
              name="dictCode"
              label="字典编码:"
              placeholder={''}
              rules={[{ required: true, message: '不能为空' }]}
            />
            <ProFormText
              name="dictName"
              label="字典值名称:"
              required={true}
              placeholder={''}
              rules={[{ required: true, message: '不能为空' }]}
            />
            <ProFormText
              name="dictValue"
              label="字典值:"
              required={true}
              placeholder={''}
              rules={[{ required: true, message: '不能为空' }]}
            />
            <ProFormDigit name={'sortOrder'} label="排序:" initialValue={1} width={'xs'} />
            <ProFormTextArea name={'description'} label="备注:" />
            <ProFormSwitch
              name={'status'}
              label={'是否使用:'}
              fieldProps={{ defaultChecked: true, checkedChildren: '是', unCheckedChildren: '否' }}
            />
          </ModalForm>,
        ]}
      />
    </>
  );
};
export default DictManage;
