import request from '@/utils/framework/request';

export const convertSearchResp = <T>(result: SearchCommonResp<T>) => {
  return {
    data: result.data.records,
    success: result.code === 2000,
    total: result.data.total,
  };
};
export async function queryDictList(payload: any) {
  return request<SearchCommonResp<any>>('/dict/getDictPageList', {
    method: 'post',
    data: payload,
  }).then(convertSearchResp);
}

export interface CommonResp<T> {
  code: number;
  data: T;
  msg: string;
}
export interface BaseSearchData<T> {
  records: T[];
  total: number;
}
export type SearchCommonResp<T> = CommonResp<BaseSearchData<T>>;

export async function setStatus(payload: any) {
  return request('/dict/setStatus', {
    method: 'get',
    params: payload,
  });
}

export async function deleteDict(payload: any) {
  return request('/dict/deleteDict', {
    method: 'get',
    params: payload,
  });
}

export async function addDict(payload: any) {
  return request('/dict/addDict', {
    method: 'post',
    data: payload,
  });
}

export async function updateDict(payload: any) {
  return request('/dict/updateDict', {
    method: 'post',
    data: payload,
  });
}
