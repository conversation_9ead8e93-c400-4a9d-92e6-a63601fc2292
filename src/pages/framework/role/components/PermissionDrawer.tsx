import { queryMenuList, setRolePermissions } from '@/api/framework/permissions';
import { Button, Checkbox, Drawer, message, Tree } from 'antd';
import React, { useEffect, useState } from 'react';
import XEUtils from 'xe-utils';
import styles from './permissionDrawer.less';
import './tree.less';

const PermissionDrawer: React.FC<any> = (props: any) => {
  const { title, visible, id, rolePermissions, onOk } = props;
  const [allExpanded, setAllExpanded] = useState<boolean>(true);
  const [allSelected, setAllSelected] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [allKeys, setAllKeys] = useState<string[]>([]);
  const [allParentKeys, setAllParentKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  // 获取权限列表
  const queryMenu = async () => {
    try {
      const res = await queryMenuList();
      if (res.code == 2000) {
        if (res.data && res.data instanceof Array) {
          const tempData = [...res.data];
          setTreeData(tempData);
          const allArr: string[] = [];
          XEUtils.eachTree(tempData, (item) => {
            allArr.push(item.key);
          });
          setAllKeys(allArr);
          const arr = XEUtils.filterTree(
            tempData,
            (item) => item.children && item.children.length > 0,
          );
          const superArr: string[] = [];
          XEUtils.eachTree(arr, (item) => {
            superArr.push(item.key);
          });
          setExpandedKeys(superArr);
          setAllParentKeys(superArr);
        }
      }
    } catch (error) {
      message.error('获取权限配置失败');
    }
  };

  // 设置权限
  const updateRolePermissions = async () => {
    setLoading(true);
    try {
      const param = {
        lastPermissionIds: rolePermissions,
        permissionIds: checkedKeys,
        roleId: id,
      };
      const res = await setRolePermissions(param);
      if (res.code != 2000) {
        message.error(res.msg);
        setLoading(false);
      } else {
        setLoading(false);
        message.success('权限设置成功!');
        if (onOk) {
          onOk();
        }
      }
    } catch (error) {
      message.error('权限设置失败');
      setLoading(false);
    }
  };
  //确定按钮点击
  const onConfirmClick = () => {
    updateRolePermissions();
  };
  useEffect(() => {
    queryMenu();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  useEffect(() => {
    setCheckedKeys(rolePermissions);
    setAllSelected(rolePermissions.length === allKeys.length);
  }, [allKeys, rolePermissions]);

  const arrayFlat = (sonArray: any) => {
    const resultFlat: any[] = [];
    sonArray.forEach((item: { key: any; children: string | any[] }) => {
      resultFlat.push(item.key);
      if (item.children?.length > 0) {
        resultFlat.push(...arrayFlat(item.children));
      }
    });
    return resultFlat;
  };

  const findCheckTree = (trees: any, key2: string) => {
    if (trees.key === key2) {
      return true;
    } else if (trees.children?.length > 0) {
      return trees.children.find((item: any) => findCheckTree(item, key2));
    }
  };

  const checkFilter = (treeArray: any, key: string) => {
    const parentKey: any = [],
      sonKey: any = [];
    function filterTreeData(treeItem: any, key1: string) {
      if (treeItem.key === key1) {
        parentKey.push(treeItem.key);
        if (treeItem.children?.length > 0) {
          sonKey.push(...arrayFlat(treeItem.children));
        }
        return true;
      } else {
        if (treeItem.children?.length > 0) {
          const checkItem = treeItem.children.find((item: any) => filterTreeData(item, key1));
          if (checkItem) {
            parentKey.push(treeItem.key);
          }
          return checkItem;
        }
      }
    }

    const checkTree = treeArray.find((item: any) => findCheckTree(item, key));
    filterTreeData(checkTree, key);
    return { parentKey, sonKey };
  };

  const onCheck = (checkedKeysValue: any, e: any) => {
    const { node, checked } = e;
    const checkedArray = checkedKeysValue?.checked;
    let resultArray: any = [];
    const { parentKey, sonKey } = checkFilter(treeData, node.key);
    resultArray = resultArray.concat(parentKey, sonKey, checkedArray);
    resultArray = Array.from(new Set(resultArray));
    if (checked === true) {
      setCheckedKeys(resultArray);
      setAllSelected(resultArray.length === allKeys.length);
    } else {
      if (sonKey.length === 0) {
        setCheckedKeys(checkedArray);
      } else {
        resultArray = resultArray.filter(
          (item: any) => sonKey.indexOf(item) < 0 && item !== node.key,
        );
        setCheckedKeys(resultArray);
      }
      setAllSelected(false);
    }
  };

  return (
    <Drawer title={title} placement={'right'} onClose={onOk} destroyOnClose open={visible}>
      <div className={styles.drawerContent}>
        <div className={styles.checkboxSuper}>
          <Checkbox
            checked={allExpanded}
            onChange={(e: any) => {
              setAllExpanded(e.target.checked);
              setExpandedKeys(e.target.checked ? allParentKeys : []);
            }}
          >
            全部展开
          </Checkbox>
          <Checkbox
            checked={allSelected}
            onChange={(e: any) => {
              setAllSelected(e.target.checked);
              setCheckedKeys(e.target.checked ? allKeys : []);
            }}
          >
            全部选中
          </Checkbox>
        </div>
        <div className={styles.treeSuper}>
          <Tree
            checkable
            onExpand={onExpand}
            checkStrictly
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={onCheck as any}
            checkedKeys={checkedKeys}
            treeData={treeData}
          />
        </div>

        <div className={styles.handleContent}>
          <Button onClick={onOk} className={styles.btn}>
            取消
          </Button>
          <Button type="primary" className={styles.btn} onClick={onConfirmClick} loading={loading}>
            确定
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default PermissionDrawer;
