import { addRole, deleteRole, getRolePageList, getRolePermissions, updateRole } from './service';
import { ApiOutlined, DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { ScrollProY, ScrollX } from '@/utils/framework/constant';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import { calcPageNo } from '@/utils/utils';
import { useAccess } from 'umi';
import PermissionDrawer from './components/PermissionDrawer';
import RoleInfoModal from './components/RoleInfoModal';
const RoleManage: React.FC = () => {
  const [rolePermissions, setRolePermissions] = useState<string[] | number[]>([]);
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [pageTotal, setTotal] = useState<any>();
  const [curRole, setCurRole] = useState<any>({
    id: '',
    roleName: '',
    roleCode: '',
    status: true,
    description: '',
  });
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const [currentPage, setCurrentPage] = useState(1);
  //更新角色信息
  const updateRoleInfo = async (roleInfo: any) => {
    try {
      const res = await updateRole(roleInfo);
      if (res.code != 2000) {
        message.error(res.msg);
      } else {
        actionRef.current?.reload();
        message.success('更新角色成功!');
      }
    } catch (error) {
      message.error('更新角色失败');
    }
  };

  //新增角色信息
  const addRoleInfo = async (roleInfo: any) => {
    try {
      const res = await addRole(roleInfo);
      if (res.code == 2000) {
        actionRef.current?.reload();
        message.success('新增角色成功!');
      }
    } catch (error) {}
  };

  //删除角色信息
  const deleteRoleInfo = async (id: string) => {
    try {
      const res = await deleteRole({ id });
      if (res.code != 2000) {
        message.error(res.msg);
      } else {
        actionRef.current?.reload();
        message.success('删除角色成功!');
        const pageNo = calcPageNo(pageTotal, currentPage, currentPageSize);
        setCurrentPage(pageNo);
      }
    } catch (error) {
      message.error('删除角色失败');
    }
  };
  //获取角色权限信息
  const getPermissionById = async (id: string) => {
    try {
      const res = await getRolePermissions({ id });
      if (res.code != 2000) {
        message.error(res.msg);
      } else {
        if (res.data && res.data instanceof Array) {
          setRolePermissions(res.data);
        } else {
          setRolePermissions([]);
        }
        setDrawerVisible(true);
      }
    } catch (error) {
      message.error('数据查询失败');
    }
  };

  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'seq',
      width: 48,
      align: 'center',
      hideInSearch: true,
      render: (_, record: any, index: number) => {
        return <span>{(currentPage - 1) * 10 + index + 1}</span>;
      },
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 200,
      align: 'left',
    },
    {
      title: '角色标识',
      dataIndex: 'roleCode',
      key: 'roleCode',
      width: 200,
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: '',
      width: 300,
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      render: (record: any) => (
        <>
          <Button
            key="edit"
            type={'link'}
            icon={<EditOutlined />}
            hidden={!access.hasBtnPerms('role-edit-button')}
            onClick={() => {
              setCurRole(record);
              setVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            key="press"
            type={'link'}
            icon={<ApiOutlined />}
            hidden={!access.hasBtnPerms('role-permission-button')}
            onClick={() => {
              setCurRole(record);
              getPermissionById(record.id.toString());
            }}
          >
            设置权限
          </Button>
          <Button
            type="link"
            size="small"
            danger
            key="batchRemove"
            hidden={!access.hasBtnPerms('role-delete-button')}
            onClick={async () => {
              Modal.confirm({
                title: '删除',
                content: '确定删除该角色吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  deleteRoleInfo(record.id);
                },
              });
            }}
          >
            <DeleteOutlined />
            删除
          </Button>
        </>
      ),
    },
  ];
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  return (
    <>
      <ProTable<any>
        bordered
        columns={columns}
        actionRef={actionRef}
        scroll={{ x: ScrollX, y: ScrollProY }}
        rowKey="id"
        key="id"
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          onChange: onShowSizeChange,
          current: currentPage,
        }}
        request={async (params) => {
          const { data: msg } = await getRolePageList({
            ...params,
            currentPage: params.current,
          });
          setTotal(msg.total);

          return {
            data: msg.records,
            success: true,
            total: msg.total,
          };
        }}
        toolBarRender={() => [
          <Button
            key="operation"
            type="primary"
            size="middle"
            icon={<PlusOutlined />}
            style={{ width: '80px' }}
            hidden={!access.hasBtnPerms('role-add-button')}
            onClick={() => {
              setCurRole({
                id: '',
                roleName: '',
                roleCode: '',
                status: true,
                description: '',
              });
              setVisible((origin) => !origin);
            }}
          >
            新建
          </Button>,
        ]}
      />

      <RoleInfoModal
        title={'新建角色'}
        visible={visible}
        onOk={(role: any) => {
          const param = {
            id: role.id,
            roleName: role.roleName,
            roleCode: role.roleCode,
            status: role.status,
            description: role.description,
          };
          if (role.id && role.id.toString().length > 0) {
            updateRoleInfo(param);
          } else {
            addRoleInfo(param);
          }
          setVisible(false);
        }}
        onCancel={() => setVisible(false)}
        defaultValue={curRole}
      />
      <PermissionDrawer
        visible={drawerVisible}
        title={'权限设置'}
        id={curRole.id}
        rolePermissions={rolePermissions}
        onOk={() => {
          setDrawerVisible(false);
        }}
      />
    </>
  );
};

export default RoleManage;
