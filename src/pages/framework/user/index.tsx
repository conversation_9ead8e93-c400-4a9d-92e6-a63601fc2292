import {
  DeleteOutlined,
  EditOutlined,
  LockOutlined,
  PlusOutlined,
  ReloadOutlined,
  SafetyCertificateOutlined,
  UnlockOutlined,
} from '@ant-design/icons';

import {
  addUser,
  deleteUser,
  getRoleList,
  getUserInfoById,
  getUserPageList,
  getUserRoles,
  resetPassword,
  updateUser,
  userFreeze,
} from './service';
import checkPasswordRule from '@/utils/framework/checkPassWord';
import { checkPhoneNumber, calcPageNo } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Form, Input, message, Modal, Radio, Space, Switch, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { useAccess } from 'umi';
import DivisionRole from './components/DivisionRoleModal';
import { ScrollCollapsedY, ScrollProX, ScrollProY } from '@/utils/framework/constant';
const validatePass = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入密码'));
  } else {
    const bad = checkPasswordRule(value);
    if (bad) {
      callback(new Error('密码不符合规则：' + bad));
    } else {
      callback();
    }
  }
};
const validatePhone = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入手机号'));
  } else {
    const bad = checkPhoneNumber(value);
    if (bad) {
      callback(new Error(bad));
    } else {
      callback();
    }
  }
};

const UserMange: React.FC = () => {
  const [forms] = Form.useForm();
  const access = useAccess();
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [pageTotal, setTotal] = useState();
  const [collapsed, setCollapsed] = useState(true);
  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 48,
      hideInSearch: true,
      align: 'center',
      key: 'index',
      render: (_, record: any, index: number) => {
        return <span>{(currentPage - 1) * currentPageSize + index + 1}</span>;
      },
    },
    {
      title: '账号名称',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      align: 'center',
    },

    {
      title: '用户名称',
      dataIndex: 'realName',
      width: 120,
      key: 'realName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '账号状态',
      dataIndex: 'lockStatus',
      key: 'lockStatus',
      width: 120,
      align: 'center',
      hideInSearch: true,
      render: (_: any, record: any) => (
        <Space>
          {record.lockStatus == 1 ? <Tag color="red">已冻结</Tag> : <Tag color="green">正常</Tag>}
        </Space>
      ),
    },
    {
      title: '所属组织',
      dataIndex: 'fullOrgName',
      key: 'fullOrgName',
      ellipsis: true,
      width: 160,
      hideInSearch: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      key: 'createTime',
      width: 180,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
      key: 'updateTime',
      width: 180,
    },
    {
      title: '操作',
      dataIndex: '',
      width: 510,
      hideInSearch: true,
      fixed: 'right',
      key: 'x',
      render: (_: any, record: any) => (
        <Space size={[8, 16]} wrap>
          <Button
            type="link"
            size="small"
            key="batchReset"
            hidden={!access.hasBtnPerms('user-resetPassword-button')}
            onClick={async () => {
              Modal.confirm({
                title: '重置密码',
                content: '确定重置密码吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  resetPassword2(record);
                },
              });
            }}
          >
            <ReloadOutlined /> 重置密码
          </Button>
          <Button
            type="link"
            size="small"
            key="editReset"
            hidden={!access.hasBtnPerms('user-edit-button')}
            onClick={() => {
              edit(record);
            }}
          >
            <EditOutlined />
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            key="giveReset"
            hidden={!access.hasBtnPerms('user-roleManage-button')}
            onClick={() => {
              showUserRoleModal(record);
            }}
          >
            <SafetyCertificateOutlined />
            分配角色
          </Button>
          <Button
            type="link"
            size="small"
            danger
            key="batchFreeze"
            hidden={!access.hasBtnPerms('user-freeze-button') || record.username == 'admin'}
            onClick={async () => {
              Modal.confirm({
                title: `${!record.lockStatus || record.lockStatus == '0' ? '冻结' : '解冻'}`,
                content: `确定${
                  !record.lockStatus || record.lockStatus == '0' ? '冻结' : '解冻'
                }该用户吗？`,
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  handleUserFreeze(record);
                },
              });
            }}
          >
            {!record.lockStatus || record.lockStatus == '0' ? <LockOutlined /> : <UnlockOutlined />}
            {!record.lockStatus || record.lockStatus == '0' ? '冻结用户' : '解冻用户'}
          </Button>
          <Button
            type="link"
            size="small"
            danger
            key="batchRemove"
            hidden={!access.hasBtnPerms('user-delete-button') || record.username == 'admin'}
            onClick={async () => {
              Modal.confirm({
                title: '删除',
                content: '确定删除该用户吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  handleUserDelete(record);
                },
              });
            }}
          >
            <DeleteOutlined /> 删除用户
          </Button>
        </Space>
      ),
    },
  ];

  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [sexValue, setSexValue] = useState();
  const [userId, setUserId] = useState<string>();
  const [userName, setUserName] = useState<string>();
  const [userNameDisabled, setUserNameDisabled] = useState(false);
  const [userIsThirdType, setUserIsThirdType] = useState(false);
  const [currentStatues, setCurrentStatues] = useState('');
  const [divisionRoleVisible, setDivisionRoleVisible] = useState(false);
  const [roleData, setRoleData] = useState([]);
  const [userRole, setUserRole] = useState([]);
  const [modalTitle, setModalTitle] = useState('新建用户');
  const actionRef = useRef<ActionType>();
  async function edit(record: any) {
    setCurrentStatues('edit');
    setModalTitle('编辑用户');
    const formData = {
      userId: record.id,
    };
    setUserId(record.id);
    if (record.thirdType === 'sso') {
      setUserIsThirdType(true);
    } else {
      setUserIsThirdType(false);
    }
    try {
      const res = await getUserInfoById(formData);
      if (res.code == 2000) {
        forms.setFieldsValue({
          username: res.data.username,
          realName: res.data.realName,
          sex: res.data.sex,
          phone: res.data.phone,
          telephone: res.data.telephone,
          email: res.data.email,
          status: res.data.status,
          fullOrgName: res.data.fullOrgName,
        });
        setUserNameDisabled(true);
        setSexValue(res.data.sex);
        setIsModalVisible(true);
      }
    } catch (error) {
      console.log(error);
    }
  }

  async function getRoleLists() {
    try {
      const res = await getRoleList();
      if (res.code == 2000) {
        setRoleData(res.data);
        return res.data;
      }
    } catch (error) {
      console.log(error);
    }
  }

  async function queryUserRoles(record: any) {
    try {
      const res = await getUserRoles({ userId: record.id });
      if (res.code == 2000) {
        setUserRole(res.data);
        setDivisionRoleVisible(true);
      }
    } catch (error) {
      console.log(error);
    }
  }

  async function showUserRoleModal(record: any) {
    setUserId(record.id);
    setUserName(record.username);
    try {
      const res = await getRoleLists();
      if (res) {
        await queryUserRoles(record);
      }
    } catch (error) {
      console.log(error);
    }
  }

  const sexOnChange = (e: any) => {
    setSexValue(e.target.value);
  };

  async function resetPassword2(record: any) {
    const formData = {
      id: record.id,
    };
    try {
      const res = await resetPassword(formData);
      if (res.code === 2000) {
        message.success(`新密码已发送至手机${record.phone}`);
      }
    } catch (error) {
      console.log(error);
    }
  }
  async function handleUserDelete(record: any) {
    const formData = {
      userId: record.id,
    };
    try {
      const res = await deleteUser(formData);
      if (res.code == 2000) {
        actionRef.current?.reload();
        message.success('删除成功');
        const pageNo = calcPageNo(pageTotal, currentPage, 10);
        setCurrentPage(pageNo);
      }
    } catch (error) {
      message.error('删除失败');
    }
  }

  async function setUserConformWithURL() {
    const formValue = forms.getFieldsValue();
    await forms.validateFields();
    const formData = {
      email: forms.getFieldValue('email') || '',
      id: userId ? userId : '',
      phone: forms.getFieldValue('phone') || '',
      realName: forms.getFieldValue('realName') || '',
      password: forms.getFieldValue('password') || '',
      sex: forms.getFieldValue('sex') || 1,
      status: formValue.status ? formValue.status : false,
      telephone: forms.getFieldValue('telephone') || '',
      username: forms.getFieldValue('username') || '',
    };
    if (currentStatues == 'add') {
      try {
        const res = await addUser(formData);
        if (res.code == 2000) {
          message.success('新建用户成功');
          actionRef.current?.reload();
          forms.resetFields();
          setIsModalVisible(false);
        } else {
          message.error('新建用户失败');
        }
      } catch (error) {
        message.error('新建用户失败');
      }
    } else {
      try {
        const res = await updateUser(formData);
        if (res.code == 2000) {
          message.success('编辑用户成功');

          actionRef.current?.reload();
          forms.resetFields();
          setIsModalVisible(false);
        } else {
          message.success('编辑用户失败');
        }
      } catch (error) {
        message.error('编辑用户失败');
      }
    }
  }

  async function handleOk() {
    setUserConformWithURL();
  }
  function handleCancle() {
    forms.resetFields();
    setIsModalVisible(false);
  }
  async function handleUserFreeze(record: any) {
    const type = !record.lockStatus || record.lockStatus == '0' ? 1 : 2;
    try {
      const res = await userFreeze(type, { username: record.username });
      if (res.code == 2000) {
        actionRef.current?.reload();
        message.success('操作成功');
      }
    } catch (error) {
      message.error('操作失败');
    }
  }
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPageSize(size);
    setCurrentPage(current);
  };
  const onCollapse = () => {
    setCollapsed(!collapsed);
  };
  return (
    <>
      <ProTable<any>
        bordered
        columns={columns}
        actionRef={actionRef}
        rowKey="id"
        key="id"
        search={{ collapsed, onCollapse }}
        scroll={{ x: ScrollProX, y: collapsed ? ScrollProY : ScrollCollapsedY }}
        pagination={{
          defaultPageSize: 10,
          onChange: onShowSizeChange,
          current: currentPage,
          showSizeChanger: true,
        }}
        request={async (params) => {
          const { data: msg } = await getUserPageList({
            ...params,
            pageSize: params.pageSize,
            current: currentPage,
            currentPage: currentPage,
          });
          setTotal(msg.total);
          return {
            data: msg.records,
            success: true,
            total: msg.total,
          };
        }}
        toolBarRender={() => [
          <Button
            key="addUser"
            type="primary"
            icon={<PlusOutlined />}
            hidden={!access.hasBtnPerms('user-add-button')}
            onClick={() => {
              setModalTitle('新建用户');
              setCurrentStatues('add');
              setUserNameDisabled(false);
              setIsModalVisible(true);
            }}
          >
            新建
          </Button>,
        ]}
      />
      <Modal
        title={modalTitle}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancle}
        closable={true}
      >
        <Form
          form={forms}
          name="advanced_search"
          className="ant-advanced-search-form"
          labelCol={{ span: 4 }}
        >
          <Form.Item
            label="账号名称"
            name="username"
            rules={[{ required: true, message: '请输入账号名称' }]}
          >
            <Input disabled={userNameDisabled} />
          </Form.Item>

          {currentStatues == 'add' && (
            <Form.Item
              label="账号密码"
              name="password"
              rules={[{ required: true, validator: validatePass }]}
            >
              <Input.Password />
            </Form.Item>
          )}
          <Form.Item
            label="姓名"
            name="realName"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input disabled={userIsThirdType} />
          </Form.Item>

          <Form.Item label="性别" name="sex">
            <Radio.Group
              onChange={sexOnChange}
              value={sexValue}
              disabled={userIsThirdType}
              defaultValue={1}
            >
              <Radio value={1}>男</Radio>
              <Radio value={2}>女</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="手机号"
            name="phone"
            rules={[{ required: true, validator: validatePhone }]}
          >
            <Input disabled={userIsThirdType} />
          </Form.Item>

          <Form.Item
            label="是否启用"
            name="status"
            valuePropName={'checked'}
            rules={[{ required: false, message: '请选择' }]}
          >
            <Switch disabled={userIsThirdType} />
          </Form.Item>

          <Form.Item
            label="固定电话"
            name="telephone"
            rules={[{ required: false, message: '请输入固定电话' }]}
          >
            <Input disabled={userIsThirdType} />
          </Form.Item>

          <Form.Item
            label="电子邮箱"
            name="email"
            rules={[{ required: false, message: '请输入电子邮箱' }]}
          >
            <Input disabled={userIsThirdType} />
          </Form.Item>

          <Form.Item label="所属组织" name="fullOrgName">
            <Input disabled={userNameDisabled} />
          </Form.Item>
        </Form>
      </Modal>
      <DivisionRole
        userName={userName}
        userID={userId}
        roleData={roleData}
        visible={divisionRoleVisible}
        userRole={userRole}
        onSubmit={() => {
          actionRef.current?.reload();
          setDivisionRoleVisible(false);
        }}
        onCancel={() => setDivisionRoleVisible(false)}
      />
    </>
  );
};

export default UserMange;
