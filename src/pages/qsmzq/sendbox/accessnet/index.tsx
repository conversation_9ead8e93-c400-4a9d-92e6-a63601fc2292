import React, { useState } from 'react';
import { message } from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import moment from 'moment';
import { ScrollProY, ScrollX } from '@/utils/framework/constant';

const WoQuery: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);

  const warning = () => {
    message.warning('功能正在建设中....');
  };

  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '工单编号',
      dataIndex: 'flowId',
      render: (dom, record: any) => <a onClick={warning}>{record.flowId}</a>,
      order: 4,
    },
    {
      title: '工单状态',
      dataIndex: 'status',
      search: false,
    },
    {
      title: '工单主题',
      dataIndex: 'title',
      order: 5,
    },
    {
      title: 'IP地址',
      dataIndex: 'manageIpaddress',
      search: false,
    },
    {
      title: '信息来源',
      dataIndex: 'resource',
      search: false,
    },
    {
      title: '建单人',
      dataIndex: 'createUser',
      search: false,
    },
    {
      title: '建单部门',
      dataIndex: 'dept',
      search: false,
    },
    {
      title: '专业',
      dataIndex: 'bussType',
      search: false,
    },
    {
      title: '建单时间',
      dataIndex: 'createTime',
      valueType: 'dateTimeRange',
      render: (dom, record: any) => (
        <span>{moment(record.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
  ];

  //Pagesize Change
  const onShowSizeChange = (current: any, size: any) => {
    setCurrentPage(current);
  };

  return (
    <>
      <ProTable<any>
        bordered
        key="flowId"
        rowKey="flowId"
        scroll={{ x: ScrollX, y: ScrollProY }}
        columns={columns}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          onChange: onShowSizeChange,
          current: currentPage,
        }}
        search={{
          span: 12,
        }}
        request={async (params) => {
          return {
            data: [
              {
                flowId: 'JR-20231023-001',
                status: '资源调整失败，原因为xxxxx',
                title: '海南-接入网-入网工单-OLT',
                resource: '电子运维手动建单',
                createUser: 'admin',
                dept: '电子运维',
                createTime: Date.now(),
                bussType: '接入网',
              },
              {
                flowId: 'JR-20231023-002',
                status: '资源调整失败，原因为xxxxx',
                title: '海南-接入网-入网工单-OLT',
                resource: '电子运维手动建单',
                createUser: 'admin',
                dept: '电子运维',
                createTime: Date.now(),
                bussType: '接入网',
              },
              {
                flowId: 'JR-20231023-003',
                status: '资源调整失败，原因为xxxxx',
                title: '海南-接入网-入网工单-OLT',
                resource: '电子运维手动建单',
                createUser: 'admin',
                dept: '电子运维',
                createTime: Date.now(),
                bussType: '接入网',
              },
            ],
            success: true,
            total: 3,
          };
        }}
      />
    </>
  );
};

export default WoQuery;
