import React, { useState, useRef, useEffect } from 'react';
import type { FormInstance } from 'antd';
import { Button, Radio } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { ScrollProY, ScrollX } from '@/utils/framework/constant';
import { useHistory } from 'umi';
import { DownloadOutlined } from '@ant-design/icons';
import { handleDefaultSheetTypeQuerySet } from '@/pages/qsmzq/utils/WoUtils';

import {
  woStatistics,
  getProCityMap,
  exportStatisticsList,
  exportStatisticsBusinessList,
} from './service';
import { getSheetTypes } from '@/pages/qsmzq/service';
import { unset } from 'lodash';

const AccessNetStatistics: React.FC = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [currentPage, setCurrentPage] = useState(1);
  const [hideCityCol, setHideCityCol] = useState(true);
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const [exportDetailLoading, setExportDetailLoading] = useState<boolean>(false);
  const [sheetTypeOptions, setSheetTypeOptions] = useState<any[]>([]);
  const [sheetType, setSheetType] = useState<string>('');

  const [provinceOpts, setProvinceOpts] = useState<any[]>([]);
  const [cityOpts, setCityOpts] = useState<any[]>([]);
  const queryProvince = () => {
    getProCityMap('').then((res) => {
      const { code, data } = res;
      const options = [];
      if (code == 200 || code == 2000) {
        for (const key in data) {
          options.push({ label: data[key], value: key, key: key });
        }
        setProvinceOpts(options);
      }
    });
  };
  const queryCity = (provinceCode: string) => {
    if (provinceCode == null || provinceCode == '') {
      setCityOpts([]);
      return;
    }
    getProCityMap(provinceCode).then((res) => {
      const { code, data } = res;
      const options = [];
      if (code == 200 || code == 2000) {
        for (const key in data) {
          options.push({ label: data[key], value: key, key: key });
        }
        setCityOpts(options);
      }
    });
  };

  // 加载流程类别下拉(根据专业查询)
  const querySheetTypes = (specialityType: string) => {
    getSheetTypes('10').then((res) => {
      const { code, data } = res;
      if (code == 200 || code == 2000) {
        const options = (data || []).map((item: any) => {
          const { sheetTypeName: label, sheetType: value } = item;
          return {
            label,
            value,
            key: value,
          };
        });
        setSheetTypeOptions(options);
      }
    });
  };

  const statOpts = [
    { label: '省分', value: 'PRO', key: 'PRO' },
    { label: '地市', value: 'CITY', key: 'CITY' },
  ];

  useEffect(() => {
    queryProvince();
    queryCity('');
    handleDefaultSheetTypeQuerySet(formRef);
    return () => {
      console.log('destroy call');
    };
  }, []);

  const columns: ProColumns<any>[] = [
    {
      title: '统计方式',
      dataIndex: 'statisticalMethod',
      initialValue: 'PRO',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: statOpts,
        initialValue: 'PRO',
        onChange: (val: string) => {
          if (val == 'CITY') {
            setHideCityCol(false);
          } else {
            setHideCityCol(true);
          }
        },
      },
    },
    {
      title: '省分',
      dataIndex: 'provinceCode',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: provinceOpts,
        onChange: (val: string) => {
          queryCity(val);
        },
      },
    },
    {
      title: '地市',
      dataIndex: 'cityCode',
      hideInTable: true,
      hideInSearch: hideCityCol,
      valueType: 'select',
      fieldProps: {
        options: cityOpts,
      },
    },
    {
      title: '省分',
      dataIndex: 'provinceName',
      search: false,
      hideInTable: true,
    },
    {
      title: '地市',
      dataIndex: 'cityName',
      search: false,
      hideInTable: true,
    },
    {
      title: '地区',
      dataIndex: 'location',
      search: false,
    },
    // {
    //   title: '流程类别',
    //   dataIndex: 'sheetType',
    //   valueType: 'select',
    //   initialValue: '0',
    //   fieldProps: {
    //     options: sheetTypeOptions,
    //   },
    //   hideInTable: true,
    // },
    {
      title: '工单总量',
      dataIndex: 'totalNum',
      align: 'center',
      search: false,
    },
    // {
    //   title: '拟稿',
    //   dataIndex: 'draft',
    //   align: 'center',
    //   search: false,
    // },
    {
      title: sheetType == '0' ? '入网审批' : '退网审批',
      dataIndex: sheetType == '0' ? 'accessExamineApprove' : 'withdrawalExamineApprove',
      align: 'center',
      search: false,
    },
    {
      title: '会签',
      dataIndex: 'countersign',
      align: 'center',
      search: false,
    },
    {
      title: '现场确认',
      hideInTable: sheetType != '1',
      dataIndex: 'onSiteConfirmation',
      align: 'center',
      search: false,
    },
    {
      title: '电路调度',
      hideInTable: sheetType != '0',
      dataIndex: 'circuitSchedule',
      align: 'center',
      search: false,
    },
    {
      title: '数据制作',
      hideInTable: sheetType != '0',
      dataIndex: 'dataProduction',
      align: 'center',
      search: false,
    },
    {
      title: '资源核对',
      dataIndex: 'resourceCheck',
      align: 'center',
      search: false,
    },
    {
      title: '省分审批',
      dataIndex: 'provincialApproval',
      align: 'center',
      search: false,
    },
    {
      title: '归档',
      dataIndex: 'filing',
      align: 'center',
      search: false,
    },
    {
      title: '建单时间',
      dataIndex: 'sheetCreateTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
      // render: (dom, record: any) => (
      //   <span>{moment(record.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
      // ),
    },
  ];

  const allColumns: ProColumns<any>[] = [
    {
      title: '统计方式',
      dataIndex: 'statisticalMethod',
      initialValue: 'PRO',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: statOpts,
        initialValue: 'PRO',
        onChange: (val: string) => {
          if (val == 'CITY') {
            setHideCityCol(false);
          } else {
            setHideCityCol(true);
          }
        },
      },
    },
    {
      title: '省分',
      dataIndex: 'provinceCode',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: provinceOpts,
        onChange: (val: string) => {
          queryCity(val);
        },
      },
    },
    {
      title: '地市',
      dataIndex: 'cityCode',
      hideInTable: true,
      hideInSearch: hideCityCol,
      valueType: 'select',
      fieldProps: {
        options: cityOpts,
      },
    },
    {
      title: '省分',
      dataIndex: 'provinceName',
      search: false,
      hideInTable: true,
    },
    {
      title: '地市',
      dataIndex: 'cityName',
      search: false,
      hideInTable: true,
    },
    {
      title: '地区',
      dataIndex: 'location',
      search: false,
    },
    {
      title: '工单总量',
      dataIndex: 'totalNum',
      align: 'center',
      search: false,
    },
    {
      title: '入网工单',
      dataIndex: 'access',
      align: 'center',
      search: false,
    },
    {
      title: '退网工单',
      dataIndex: 'withdrawal',
      align: 'center',
      search: false,
    },
    {
      title: '割接工单',
      dataIndex: 'cutover',
      align: 'center',
      search: false,
    },
    {
      title: '建单时间',
      dataIndex: 'sheetCreateTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
    },
  ];

  const columnsCutover: ProColumns<any>[] = [
    {
      title: '统计方式',
      dataIndex: 'statisticalMethod',
      initialValue: 'PRO',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: statOpts,
        initialValue: 'PRO',
        onChange: (val: string) => {
          if (val == 'CITY') {
            setHideCityCol(false);
          } else {
            setHideCityCol(true);
          }
        },
      },
    },
    {
      title: '省分',
      dataIndex: 'provinceCode',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: provinceOpts,
        onChange: (val: string) => {
          queryCity(val);
        },
      },
    },
    {
      title: '地市',
      dataIndex: 'cityCode',
      hideInTable: true,
      hideInSearch: hideCityCol,
      valueType: 'select',
      fieldProps: {
        options: cityOpts,
      },
    },
    {
      title: '省分',
      dataIndex: 'provinceName',
      search: false,
      hideInTable: true,
    },
    {
      title: '地市',
      dataIndex: 'cityName',
      search: false,
      hideInTable: true,
    },
    {
      title: '地区',
      dataIndex: 'location',
      search: false,
    },
    // {
    //   title: '流程类别',
    //   dataIndex: 'sheetType',
    //   valueType: 'select',
    //   initialValue: '0',
    //   fieldProps: {
    //     options: sheetTypeOptions,
    //   },
    //   hideInTable: true,
    // },
    {
      title: '工单总量',
      dataIndex: 'totalNum',
      align: 'center',
      search: false,
    },
    // {
    //   title: '拟稿',
    //   dataIndex: 'draft',
    //   align: 'center',
    //   search: false,
    // },
    {
      title: '割接审批',
      dataIndex: 'cutoverApproval',
      align: 'center',
      search: false,
    },
    {
      title: '会签',
      dataIndex: 'countersign',
      align: 'center',
      search: false,
    },
    {
      title: '现场施工',
      dataIndex: 'onSiteConstruction',
      align: 'center',
      search: false,
    },
    {
      title: '电路调度',
      dataIndex: 'circuitSchedule',
      align: 'center',
      search: false,
    },
    {
      title: '数据制作',
      dataIndex: 'dataProduction',
      align: 'center',
      search: false,
    },
    {
      title: '资源核对',
      dataIndex: 'resourceCheck',
      align: 'center',
      search: false,
    },
    {
      title: '省分审批',
      dataIndex: 'provincialApproval',
      align: 'center',
      search: false,
    },
    {
      title: '归档',
      dataIndex: 'filing',
      align: 'center',
      search: false,
    },



    {
      title: '建单时间',
      dataIndex: 'sheetCreateTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
      // render: (dom, record: any) => (
      //   <span>{moment(record.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
      // ),
    },
  ];

  //Pagesize Change
  const onShowSizeChange = (current: any) => {
    setCurrentPage(current);
  };

  const handleExport = () => {
    const query = formRef.current?.getFieldsValue();
    query.sheetType = sheetType;
    setExportLoading(true);
    exportStatisticsList(query, () => {
      setExportLoading(false);
    });
  };

  const handleExportBusiness = () => {
    const query = formRef.current?.getFieldsValue();
    query.sheetType = sheetType;
    setExportDetailLoading(true);
    exportStatisticsBusinessList(query, () => {
      setExportDetailLoading(false);
    });
  };

  const reload = () => {
    actionRef.current?.reload();
  };

  // 每次页面打开时刷新表格列表
  const history = useHistory();
  useEffect(() => {
    querySheetTypes('');
    const unlisten = history.listen((location) => {
      if (location.pathname == '/qsmzq/statistics/accessnet') {
        reload();
      }
    });
    return () => {
      console.log(' unlisten ');
      unlisten();
    };
  }, [history]);

  return (
    <>
      <ProTable<any>
        actionRef={actionRef}
        bordered
        key="flowId"
        rowKey="flowId"
        scroll={{ x: ScrollX, y: ScrollProY }}
        columns={sheetType == '' ? allColumns : sheetType == '2' ? columnsCutover : columns}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          onChange: onShowSizeChange,
          current: currentPage,
        }}
        formRef={formRef}
        form={{
          labelWidth: 80,
        }}
        search={{
          span: 8,
          labelWidth: 80,
          defaultCollapsed: false,
        }}
        request={async (params) => {
          const { current: pageNum, pageSize } = params;
          const { ...props } = formRef.current?.getFieldsValue(true);
          try {
            const query = {
              pageNum,
              pageSize,
              // sheetType: 0,
              ...props,
              sheetType,
            };
            console.log('query', query);
            const { code, data } = await woStatistics(query);
            if (code == 200 || code == 2000) {
              const { total, records } = data;
              return {
                data: records as any[],
                total,
                success: true,
              };
            } else {
              return { data: [], total: 0, success: false };
            }
          } catch (err) {
            return { data: [], total: 0, success: false };
          }
        }}
        toolbar={{
          search: (
            <Radio.Group
              value={sheetType}
              buttonStyle="solid"
              optionType={'button'}
              options={[{ label: '全部', value: '', key: 'all' }, ...sheetTypeOptions]}
              onChange={(val) => {
                console.log('reload ');
                setSheetType(val.target.value);
                reload();
              }}
            />
          ),
        }}
        toolBarRender={() => [
          <Button
            key="exportBusiness"
            loading={exportDetailLoading}
            type="primary"
            onClick={handleExportBusiness}
          >
            工单明细导出
          </Button>,
          <Button key="exportList" loading={exportLoading} type="primary" onClick={handleExport}>
            报表导出
          </Button>,
          // <Button
          //   key="exportList"
          //   title="导出数据"
          //   type="link"
          //   loading={exportLoading}
          //   icon={<DownloadOutlined />}
          //   onClick={handleExport}
          //   style={{ color: 'unset' }}
          // />,
        ]}
      />
    </>
  );
};

export default AccessNetStatistics;
