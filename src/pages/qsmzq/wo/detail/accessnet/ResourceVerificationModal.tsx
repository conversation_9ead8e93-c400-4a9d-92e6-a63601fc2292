import {
  Button,
  Card,
  Col,
  Descriptions,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Tabs,
  Upload,
} from 'antd';
import { useEffect, useRef, useState } from 'react';
import type { AccessNetType, WorkOrderRecord } from '@/pages/qsmzq/data.d';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {
  // getAuditParticipants,
  getAuditProvinceUsers,
  resourceCheckPass,
  resourceCheckReject2DataProd,
  resourceCheckRejectToDraft,
} from '@/pages/qsmzq/service';
import {
  buildParticipantOptions,
  buildWorkFlowData,
  getNextParticipants,
  getParticipantIds,
} from '@/pages/qsmzq/utils/WoUtils';
import type { FlowLogRecord } from '@/pages/qsmzq/wo/detail/data';
import { autoCheckResourceAndQuery, checkResource } from '@/pages/qsmzq/wo/detail/service';
import { enumListGroup } from '@/pages/qsmzq/woadd/service';
import PositIdSelect from '@/pages/qsmzq/woadd/components/PositIdSelect';
import RegionSelect from '@/pages/qsmzq/woadd/components/RegionSelect';
import { validateIP } from '@/utils/framework/validate';
import MntManSelect from '@/pages/qsmzq/woadd/components/MntManSelect';
import MfrSelect from '@/pages/qsmzq/woadd/components/MfrSelect';
import EqpModelSelect from '@/pages/qsmzq/woadd/components/EqpModelSelect';
import CardModelSelect from '@/pages/qsmzq/woadd/components/CardModelSelect';
import CardTypeSelect from '@/pages/qsmzq/woadd/components/CardTypeSelect';
import DatePickerAsString from '@/pages/qsmzq/components/DatePickerAsString';
import type { WorkOrderDetail } from '@/pages/qsmzq/data.d';
import type { UploadFile } from 'antd/es/upload/interface';
import { UploadOutlined } from '@ant-design/icons';
import { getStore } from '@/utils/framework/store';
import { attachList, deleteAttach, downloadAttach } from '@/pages/qsmzq/wo/detail/service';
import moment from 'moment';

interface ComponentProps {
  open: boolean;
  setOpen: (val: boolean) => void;
  onComplete: () => void;
  // 入网或退网
  netType?: AccessNetType | string;
  woRecord: WorkOrderRecord;
  showAsDescriptions?: boolean;
  // 操作记录日志
  flowLogRecord?: FlowLogRecord;
  workOrderDetail?: WorkOrderDetail;
  // 环节编码
  activityCode: string;
  // 资源实例id
  resInstId: string;
  // 附件组id
  attachGroupId?: string;
  // 省份下拉
  proParticipantOptions?: any[];
  // 非省份下拉
  unproParticipantOptions?: any[];
}

// 资源类型
type ResourceType = 'OLT' | '板卡' | '端口';
type ResourceTypeName = ResourceType | 'IP网端口' | 'PON端口';
type PortType = 'none' | 'ipnetwork' | 'pon';

interface ResourceVerifi {
  xh?: number;
  woResId: string;
  type: ResourceType;
  typeName: ResourceTypeName;
  name: string;
  checkResult: boolean;
  checkDesc: string;
  checkTime: string;
  children?: ResourceVerifi[] | null;
  //资源详情字段
  detail: any;
  // 退网核对失败列表
  failCheckResult?: any;
}

type Operator = 'pass' | 'reject2dataProduction' | 'reject2draft';

interface SubmitActionForm {
  operator: Operator;
  submitToProv: boolean;
  note?: string;
  remark?: string;
  participantIds: string[];
}

export default ({
  open,
  setOpen,
  onComplete,
  woRecord,
  workOrderDetail,
  showAsDescriptions,
  flowLogRecord,
  netType = 'ranAccess',
  activityCode,
  resInstId,
  attachGroupId = '',
  proParticipantOptions,
  unproParticipantOptions,
}: ComponentProps) => {
  const [modal, contextHolder] = Modal.useModal();
  const [actionForm, setActionForm] = useState<SubmitActionForm>({
    operator: 'pass',
    submitToProv: true,
    participantIds: [],
  });

  const tempForm = useRef<SubmitActionForm>({ ...actionForm });

  // 审核人下拉
  const [participantOptions, setParticipantOptions] = useState<any[]>([]);

  const [checkAllResult, setCheckAllResult] = useState<boolean>(false);
  const [tableData, setTableData] = useState<ResourceVerifi[]>([]);

  // 枚举字段列表
  const [enumMap, setEnumMap] = useState<any>({});
  const [checkModalVisible, setCheckModalVisible] = useState<boolean>();
  const [resourceType, setResourceType] = useState<ResourceType>('OLT');
  const [modalTitle, setModalTitle] = useState<string>('OLT核对详情');
  const [okLoading, setOkLoading] = useState<boolean>();
  const [checkLoading, setCheckLoading] = useState<boolean>();

  const [checkForm] = Form.useForm();
  const [checkFormResult, setCheckFormResult] = useState<boolean>(false);
  const [portType, setPortType] = useState<PortType>('none');
  // 资源OLT核对
  const [positTypeId, setPositTypeId] = useState<string>('');

  const RES_TYPE_ID_IPNETWORK = '2411';

  // 核对失败详情
  const [failModalVisible, setFailModalVisible] = useState<boolean>(false);
  const [failList, setFailList] = useState<any[]>([]);
  const setParticipants = (isProvince = false) => {
    const options = isProvince ? proParticipantOptions : unproParticipantOptions;
    console.log('setParticipants', options);
    setParticipantOptions(options as any[]);
  };
  const queryEqpDeviceEnumMap = () => {
    enumListGroup([
      'mntType',
      'propChar',
      'propertyBelong',
      'positType',
      'speciality',
      'belongNetwork',
      'portType',
      'portTypeId',
      'portProtocalType',
      'machineChar',
      'portLevel',
      'portRate',
      'oprState',
      'portWavelength',
      'portStatus',
      'protocolStatus',
      'mntState',
      'buildModel',
      'coopModel',
      'datacollectState',
      'technicalMode',
      'lifeCycle',
      'pConfirmStatus',
    ]).then((res) => {
      const { code, data } = res;
      if (code == 200 || code == 2000) {
        setEnumMap(data);
      }
    });
  };

  // 弹出核对详情框
  const showDetail = (row: ResourceVerifi) => {
    console.log(row);
    const type = row.type;
    let ptype: PortType = 'none';
    setResourceType(type);

    setModalTitle(`${type}核对详情`);
    checkForm.resetFields();
    checkForm.setFieldsValue(row.detail);
    setCheckModalVisible(true);

    if (type == '端口') {
      const resTypeId = row.detail.resTypeId;
      if (resTypeId == RES_TYPE_ID_IPNETWORK) {
        ptype = 'ipnetwork';
      } else {
        ptype = 'pon';
      }
    }
    setPortType(ptype);
    setPositTypeId(row.detail.positTypeId);
    // 2024/02/04 修改为hjCheckResult为准
    setCheckFormResult(row.detail.hjCheckResult == '核对成功');
    console.log('row.detail ', row.detail);
  };

  const showFailDetal = (row: ResourceVerifi) => {
    let list = [];

    try {
      const json = row.failCheckResult as string;
      list = JSON.parse(json);
    } catch (e) { }
    console.log("const showFailDetal = (row: ResourceVerifi) =>", row)
    list.forEach((item: any) => {
      item['resName'] = item?.['resName'].length == 0 ? row['name'] : item['resName'];
    })
    setFailList(list);
    setFailModalVisible(true);
  };

  const columns: ProColumns<ResourceVerifi>[] = [
    {
      title: '序号',
      dataIndex: 'xh',
      align: 'center',
    },
    {
      title: '资源类型',
      dataIndex: 'typeName',
      align: 'center',
    },
    {
      title: '资源名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '核对结果',
      dataIndex: 'checkResult',
      render: (_, row) => {
        return row.checkResult ? (
          <div>核对成功</div>
        ) : (
          <div style={{ color: 'red' }}>核对不通过</div>
        );
      },
      align: 'center',
    },
    {
      title: '描述说明',
      dataIndex: 'checkDesc',
      align: 'center',
      render: (_, row) => {
        if (netType == 'ranAccess' || row.checkResult) {
          return <div>{row.checkDesc}</div>;
        } else {
          // 退网且失败
          return (
            <Button
              type={'link'}
              title={row.checkDesc}
              onClick={() => {
                showFailDetal(row);
              }}
            >
              查看失败详情
            </Button>
          );
        }
      },
    },
    {
      title: '核对时间',
      dataIndex: 'checkTime',
      align: 'center',
    },
    {
      title: '人工核对',
      dataIndex: 'detail',
      hideInTable: netType != 'ranAccess',
      render: (_, row) => {
        return (
          <Button
            disabled={netType != 'ranAccess'}
            type={'link'}
            size={'small'}
            onClick={() => showDetail(row)}
          >
            核对详情
          </Button>
        );
      },
      align: 'center',
    },
  ];

  const updateActionForm = (field: string, val: any) => {
    tempForm.current[field] = val;
    const form: any = { ...tempForm.current };
    setActionForm(form);
  };
  const headers = {
    'x-token': getStore('Token'),
  };
  // 附件列表(可初次通过接口获取)
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  // 上传最大文件大小
  const MAX_FILE_SIZE = 20 * 1024 * 1024;
  const accepts: string[] = [
    '.txt',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
    '.pdf',
    '.zip',
    '.rar',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.heic',
  ];
  const handleBeforeUpload = (file: UploadFile, fl: UploadFile[]) => {
    const uploadedCnt = fileList.filter((f) => f.status == 'done').length;
    if (fl.length > 5 - uploadedCnt) {
      message.destroy();
      if (uploadedCnt > 0) {
        if (uploadedCnt >= 5) {
          message.error(
            `最多支持上传5个附件,已上传${uploadedCnt}个附件,请先删除部分附件再上传新的附件`,
          );
        } else {
          message.error(
            `最多支持上传5个附件,已上传${uploadedCnt}个附件,最多选择${5 - uploadedCnt}个`,
          );
        }
      } else {
        message.error('最多支持上传5个附件');
      }
      return Upload.LIST_IGNORE;
    }
    const size: number = file.size as number;
    if (size > MAX_FILE_SIZE) {
      message.error(`附件[${file.name}]大小超过20MB,不能上传`);
      file.status = 'error';
      file.response = `附件[${file.name}]大小超过20MB,不能上传`;
      return Upload.LIST_IGNORE;
    }
    const name = file.name as string;
    const accept = accepts.find((acc) => name.endsWith(acc));
    if (!accept) {
      message.error(`附件[${file.name}]类型不支持`);
      file.status = 'error';
      file.response = `附件[${file.name}]类型不支持`;
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleFileRemove = (file: UploadFile) => {
    if (file.status != 'done') return true;
    return new Promise<void>(async (resolve, reject) => {
      modal.confirm({
        width: 300,
        centered: true,
        title: '',
        content: '确认删除吗',
        onOk: () => {
          deleteAttach(file.uid).then((res) => {
            const { code, msg = '服务端错误' } = res;
            if (code == 200 || code == 2000) {
              message.info(`附件[${file.name}]已删除`);
              resolve();
            } else {
              message.destroy();
              message.error(msg || '服务端错误');
              reject();
            }
          });
        },
        onCancel: () => {
          reject();
        },
      });
    });
  };
  const resetHandleFileRemove = (file: UploadFile) => {
    deleteAttach(file.uid).then((res) => {
      // const { code, msg = '服务端错误' } = res;
      // if (code == 200 || code == 2000) {
      //   message.info(`附件[${file.name}]已删除`);
      // } else {
      //   message.destroy();
      //   message.error(msg || '服务端错误');
      // }
    });
  };

  // 加载附件列表
  const loadAttachList = () => {
    const groupKey = attachGroupId as string;
    if (groupKey) {
      attachList(groupKey).then((res) => {
        const { code, data } = res;
        if (code == 200 || code == 2000) {
          setFileList(
            (data || []).map((item: any) => {
              console.log('file item', item);
              const { attId, attOrigName } = item;
              return {
                uid: attId,
                name: attOrigName,
                status: 'done',
              };
            }),
          );
        } else {
          message.destroy();
          message.error('附件列表加载失败');
        }
      });
    } else {
      console.warn('groupKey is null or empty when load attachList');
    }
  };

  const handleUploadChange = (info: any) => {
    console.log('info', info);
    // 当前上传的附件
    const f = info.file as UploadFile;
    if (f.status != 'removed') {
      if (f && f.response) {
        console.log('f.status', f.status);
        const { code, data } = f.response;
        if (code) {
          if (code != 200 && code != 2000) {
            message.error(`附件[${f.name}]上传失败`);
            f.response = `附件[${f.name}]上传失败`;
            f.status = 'error';
          } else {
            message.info(`附件[${f.name}]上传成功`);
            f.uid = data.id;
          }
        } else {
          f.response = `附件[${f.name}]上传失败: 服务端错误`;
          f.status = 'error';
        }
      }
    }
    const newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const handleFileDownload = (file: UploadFile) => {
    console.log('download file ', file);
    const uid = file.uid;
    downloadAttach(uid, file.name);
  };

  const OperatorDescriptions = (
    <Descriptions
      title={'人工核对'}
      size="small"
      column={1}
      bordered
      labelStyle={{ width: '30%', textAlign: 'center' }}
    >
      <Descriptions.Item
        label={
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>操作选项
          </span>
        }
      >
        <Radio.Group
          disabled={showAsDescriptions}
          value={actionForm.operator}
          onChange={(val) => updateActionForm('operator', val.target.value)}
        >
          <Radio
            // 暂时
            // disabled={false}
            disabled={!checkAllResult || showAsDescriptions}
            value={'pass'}>
            通过
          </Radio>
          {netType == 'ranAccess' && (
            <Radio
              disabled={/*checkAllResult ||*/ showAsDescriptions}
              value={'reject2dataProduction'}
            >
              驳回至数据制作
            </Radio>
          )}
          <Radio value={'reject2draft'}>驳回至拟稿</Radio>
        </Radio.Group>
      </Descriptions.Item>
      {actionForm.operator == 'pass' && (
        <>
          <Descriptions.Item
            label={
              <span>
                <span style={{ color: 'red', marginRight: '2px' }}>*</span>是否省分审批
              </span>
            }
          >
            <Radio.Group
              disabled={showAsDescriptions}
              value={actionForm.submitToProv}
              onChange={
                (val) => {
                  setParticipants(val.target.value);
                  updateActionForm('submitToProv', val.target.value);
                }
                // updateActionForm('submitToProv', val.target.value)
              }
            >
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Descriptions.Item>
          {actionForm.submitToProv && (
            <Descriptions.Item
              label={
                <span>
                  <span style={{ color: 'red', marginRight: '2px' }}>*</span>省分审批人员
                </span>
              }
            >
              <Select
                loading={participantOptions.length == 0}
                value={actionForm.participantIds}
                disabled={showAsDescriptions}
                mode={'multiple'}
                showSearch
                optionFilterProp="label"
                placeholder={'请选择处理人'}
                options={participantOptions}
                style={{ width: '100%' }}
                onChange={(val) => updateActionForm('participantIds', val)}
              />
            </Descriptions.Item>
          )}
        </>
      )}
      <Descriptions.Item label="审核意见">
        <Input
          disabled={showAsDescriptions}
          placeholder={'请填写审核意见'}
          value={actionForm.note}
          onChange={(evt) => updateActionForm('note', evt.target.value)}
        />
      </Descriptions.Item>
      <Descriptions.Item label="附件">
        <Upload
          disabled={!attachGroupId}
          action={`${API_URL}/qsmzq-common/attach/uploadSingle`}
          accept={accepts.join(',')}
          name={'file'}
          headers={headers}
          fileList={fileList}
          showUploadList={{
            showRemoveIcon: !showAsDescriptions,
            showDownloadIcon: true,
          }}
          multiple
          maxCount={5}
          data={{
            groupKey: attachGroupId,
            sheetCreateTime:
              workOrderDetail?.sheetCreateTime || moment().format('YYYY-MM-DD HH:mm:ss'),
          }}
          beforeUpload={handleBeforeUpload}
          onRemove={handleFileRemove}
          onChange={(info) => handleUploadChange(info)}
          onDownload={(file) => handleFileDownload(file)}
        >
          <Button
            disabled={showAsDescriptions || !attachGroupId}
            type={'primary'}
            icon={<UploadOutlined />}
          >
            点击上传
          </Button>

        </Upload>
        <span style={{ display: 'block', fontSize: '12px' }}>
          （支持上传类型.txt, .doc, .docx, .xls, .xlsx, .ppt, .pdf, .pptx,.pdf, .zip, .rar, .jpg, .jpeg, .png, .gif, .heic最大不超过20M）
        </span>
      </Descriptions.Item>
      <Descriptions.Item label="备注说明">
        <Input
          disabled={showAsDescriptions}
          placeholder={'请填写备注说明'}
          value={actionForm.remark}
          allowClear
          onChange={(evt) => updateActionForm('remark', evt.target.value)}
        />
      </Descriptions.Item>
    </Descriptions>
  );

  /**
   * 后台数据转换为资源核对模型
   *
   * @param data
   */
  const transformResourceVerifi = (
    data: any[],
  ): { resultFlag: boolean; resources: ResourceVerifi[] } => {
    let resultFlag = true;
    const resourceVerifis: ResourceVerifi[] = [];
    let xh = 0;
    for (const item of data) {
      const { checkResult, checkDesc, failCheckResult, checkTime, cardInfos, ...detail } = item;
      // 设备资源
      const resourceVerifi: ResourceVerifi = {
        xh: ++xh,
        woResId: detail.woResId,
        type: 'OLT',
        typeName: 'OLT',
        name: detail.eqpName,
        checkResult: checkResult == '核对成功',
        checkDesc,
        failCheckResult,
        checkTime,
        detail: {
          ...detail,
          checkResult,
        },
      };
      if (!resourceVerifi.checkResult) {
        resultFlag = false;
      }
      resourceVerifis.push(resourceVerifi);
      // 板卡资源
      const cardChildren: ResourceVerifi[] = [];
      resourceVerifi.children = cardChildren;
      for (const cardInfo of cardInfos || []) {
        const {
          checkResult: cardCheckResult,
          checkDesc: cardCheckDesc,
          checkTime: cardCheckTime,
          portInfos,
          ...cardDetail
        } = cardInfo;
        if (cardCheckResult != '核对成功') {
          resultFlag = false;
        }
        const cardVerifi: ResourceVerifi = {
          xh: ++xh,
          woResId: cardDetail.woResId,
          type: '板卡',
          typeName: '板卡',
          name: cardDetail.cardName,
          checkResult: cardCheckResult == '核对成功',
          checkDesc: cardCheckDesc,
          checkTime: cardCheckTime,
          detail: { ...cardDetail, checkResult: cardCheckResult },
        };
        cardChildren.push(cardVerifi);

        // 端口资源
        const portChildren: ResourceVerifi[] = [];
        cardVerifi.children = portChildren;
        for (const portInfo of portInfos || []) {
          const {
            checkResult: portCheckResult,
            checkDesc: portCheckDesc,
            checkTime: portCheckTime,
            ...portDetail
          } = portInfo;
          if (portCheckResult != '核对成功') {
            resultFlag = false;
          }

          const portVerifi: ResourceVerifi = {
            xh: ++xh,
            woResId: portDetail.woResId,
            type: '端口',
            typeName: portDetail.resTypeId == RES_TYPE_ID_IPNETWORK ? 'IP网端口' : 'PON端口',
            name: portDetail.portName,
            checkResult: portCheckResult == '核对成功',
            checkDesc: portCheckDesc,
            checkTime: portCheckTime,
            detail: { ...portDetail, checkResult: portCheckResult },
          };
          portChildren.push(portVerifi);
        }
      }
      if (cardChildren.length == 0) {
        resourceVerifi.children = null;
      }
    }
    return { resultFlag, resources: resourceVerifis };
  };

  // const loadCheckResourceInfos = () => {
  //   setTableData([]);
  //   queryCheckResourceInfos(woRecord.woId as string, netType as NetWorkType).then((res) => {
  //     const { code, data } = res;
  //     console.log('res', res);
  //     if (code == 200 || code == 2000) {
  //       const { resultFlag, resources } = transformResourceVerifi(data);
  //       setTableData(resources);
  //       setCheckAllResult(resultFlag);
  //     }
  //   });
  // };

  // 自动核对
  const autoCheckAndQuery = () => {
    setCheckLoading(true);
    setTableData([]);
    autoCheckResourceAndQuery(woRecord.woId as string, netType as AccessNetType, resInstId)
      .then((res) => {
        const { code, data, status } = res;
        if (code == 200 || code == 2000) {
          message.info('核对完成');
          const { resultFlag, resources } = transformResourceVerifi(data);
          setTableData(resources);
          setCheckAllResult(resultFlag);
          if (resultFlag) {
            if (
              actionForm.operator == 'reject2dataProduction' ||
              actionForm.operator == 'reject2draft'
            ) {
              // 核对通过，并且操作是驳回数据制作/驳回拟稿时重置
              updateActionForm('operator', 'pass');
            }
          } else {
            if (actionForm.operator == 'pass') {
              // 核对不通过，并且操作是通过时重置
              updateActionForm(
                'operator',
                netType == 'ranAccess' ? 'reject2dataProduction' : 'reject2draft',
              );
            }
          }
        } else {
          message.destroy();
          message.error('核对失败' + (status == 500 ? '：服务端错误' : ''));
          if (actionForm.operator == 'pass') {
            // 核对不通过，并且操作是通过时重置
            updateActionForm(
              'operator',
              netType == 'ranAccess' ? 'reject2dataProduction' : 'reject2draft',
            );
          }
        }
      })
      .catch((err) => {
        setCheckAllResult(false);
        message.error('核对失败');
        console.error(err);
      })
      .finally(() => {
        setCheckLoading(false);
      });
  };
  useEffect(() => {
    if (proParticipantOptions && unproParticipantOptions) {
      setParticipants(actionForm.submitToProv);
    }
    //setParticipants(actionForm.submitToProv);
  }, [proParticipantOptions, unproParticipantOptions]);
  // 更新
  useEffect(() => {
    // 工单详情 - 操作表单回显
    if (flowLogRecord) {
      const form = Object.assign(
        { operator: 'pass', submitToProv: true, note: '' },
        {
          operator: flowLogRecord.actionName,
          note: flowLogRecord.processSuggestion,
          remark: flowLogRecord.remark || flowLogRecord.opRequest?.remark,
          submitToProv: !!flowLogRecord.opRequest.isProvincialApproval,
          participantIds: getParticipantIds(flowLogRecord.opRequest?.workFlow?.nextParticipant),
        },
      );
      // 更新操作表单
      setActionForm(form as SubmitActionForm);
      // 下拉虚拟options
      setParticipantOptions(
        buildParticipantOptions(flowLogRecord.opRequest?.workFlow?.nextParticipant),
      );
      //加载附件
      loadAttachList();
    }
    // 查询核对资源列表
    if (open) {
      if (woRecord) {
        // loadCheckResourceInfos();
        autoCheckAndQuery();
        // 加载枚举
        queryEqpDeviceEnumMap();
      }

      // 非readonly下加载审核人列表
      setParticipants(true);
      // getAuditProvinceUsers({
      //   processNode: activityCode,
      //   processName: netType,
      //   specialty: netType,
      //   sheetNo: woRecord.sheetNo,
      //   professionalType: '10',
      // })
      //   .then((res) => {
      //     const { code, data } = res;
      //     if (code == 200 || code == 2000) {
      //       const options = (Array.isArray(data) ? data : []).map(
      //         ({ userName, trueName, showName }) => {
      //           return {
      //             key: userName,
      //             value: userName,
      //             label: showName,
      //             trueName,
      //           };
      //         },
      //       );
      //       setParticipantOptions(options);
      //       updateActionForm('participantIds', []);
      //     }
      //   })
      //   .catch(() => {});
    }
  }, [flowLogRecord, woRecord, open]);

  if (showAsDescriptions) {
    return OperatorDescriptions;
  }

  const submit = () => {
    // validate something
    const flowData = buildWorkFlowData(woRecord);
    flowData.note = actionForm.note;
    flowData.remark = actionForm.remark;
    if (actionForm.operator == 'pass') {
      // 暂时
      if (!checkAllResult) {
        message.error('当前资源列表核对未通过');
        return;
      }
      // 校验是否核对通过
      flowData.isProvincialApproval = actionForm.submitToProv;

      if (flowData.isProvincialApproval) {
        const nextParticipants = getNextParticipants(actionForm.participantIds, participantOptions);
        // 设置下一步提交人
        if (nextParticipants.length == 0) {
          message.warning('请选择处理人');
          return;
        }
        flowData.workFlow.nextParticipant = nextParticipants;
      }
      setOkLoading(true);
      console.log('flowData', flowData);
      resourceCheckPass(flowData, netType as AccessNetType)
        .then((res) => {
          console.log('res', res);
          const { code, msg = '服务端错误' } = res;
          if (code == 200 || code == 2000) {
            message.info('操作成功');
            setOpen(false);
            onComplete();
            // 返回
          } else {
            const { statusText } = res;
            message.destroy();
            message.error('操作失败: ' + (msg || statusText));
          }
        })
        .catch((err) => {
          console.log('err', err);
          message.error('操作失败: ' + err);
        })
        .finally(() => {
          setOkLoading(false);
        });
    } else if (actionForm.operator == 'reject2dataProduction') {
      if (checkAllResult) {
        // message.error('当前资源列表核对已通过，不支持驳回数据制作');
        // return;
      }
      setOkLoading(true);
      resourceCheckReject2DataProd(flowData, netType as AccessNetType)
        .then((res) => {
          const { code, msg = '服务端错误' } = res;
          if (code == 200 || code == 2000) {
            message.info('操作成功');
            setOpen(false);
            onComplete();
            // 返回
          } else {
            message.destroy();
            message.error('操作失败: ' + msg);
          }
        })
        .catch((err) => {
          message.error('操作失败: ' + err);
        })
        .finally(() => {
          setOkLoading(false);
        });
    } else {
      setOkLoading(true);
      resourceCheckRejectToDraft(flowData, netType as AccessNetType)
        .then((res) => {
          const { code, msg = '服务端错误' } = res;
          if (code == 200 || code == 2000) {
            message.info('操作成功');
            setOpen(false);
            onComplete();
            // 返回
          } else {
            message.destroy();
            message.error('操作失败: ' + msg);
          }
        })
        .catch((err) => {
          message.error('操作失败: ' + err);
        })
        .finally(() => {
          setOkLoading(false);
        });
    }
  };

  // 人工核对
  const manualCheck = () => {
    // 资源数据
    const resouce = checkForm.getFieldsValue(true);
    console.log('input resouce', resouce);
    checkForm.validateFields().then(() => {
      let promise = null;
      if (resourceType == 'OLT') {
        // 设备
        promise = checkResource(resouce, netType as AccessNetType, 'eqp', resInstId);
      } else if (resourceType == '板卡') {
        // 板卡
        promise = checkResource(resouce, netType as AccessNetType, 'card', resInstId);
      } else {
        // 端口
        promise = checkResource(resouce, netType as AccessNetType, 'port', resInstId);
      }

      // 设置loading
      setCheckLoading(true);
      // check result
      promise
        .then((res) => {
          const { code, status } = res;
          if (code == 200 || code == 2000) {
            message.info('保存成功');
            // 刷新
            // loadCheckResourceInfos();
            autoCheckAndQuery();
            setCheckModalVisible(false);
          } else {
            console.log(res);
            message.destroy();
            message.error('核对失败' + (status == 500 ? '：服务端错误' : ''));
          }
        })
        .catch((err) => {
          message.destroy();
          message.error('操作失败');
          console.error(err);
        })
        .finally(() => {
          setCheckLoading(false);
        });
    });
  };

  const getRequiredFormItems = () => {
    if (resourceType == 'OLT') {
      // OLT设备的表单字段信息
      return [
        <Col span={8} key={'eqpName'}>
          <Form.Item
            required
            label={'设备名称'}
            name={'eqpName'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'blur', message: '请输入设备名称' }]}
          >
            <Input placeholder={'请输入设备名称'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'eqpNo'}>
          <Form.Item
            label={'设备编号'}
            name={'eqpNo'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'blur', message: '请输入设备编号' }]}
          >
            <Input placeholder={'请输入设备编号'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'positTypeId'}>
          <Form.Item
            required
            label={'安置地点类型'}
            name={'positTypeId'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择安置地点类型' }]}
          >
            <Select
              placeholder={'请选择安置地点类型'}
              allowClear
              options={(enumMap.positType || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
              onChange={(val) => {
                setPositTypeId(val);
                // 重置安置地点
                checkForm.setFieldValue('positId', null);
                checkForm.setFieldValue('positName', null);
              }}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'positId'}>
          <Form.Item
            label={'所属安置地点'}
            name={'positId'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择所属安置地点' }]}
          >
            <PositIdSelect
              form={checkForm}
              columnId={'positId'}
              columnName={'positName'}
              positTypeId={positTypeId}
              regionReadonly={checkFormResult}
              onChange={(value: any, record: any) => {
                checkForm.setFieldValue('regionId', record.regionId);
                checkForm.setFieldValue('regionName', record.regionName || record.regionId);
              }}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'regionId'}>
          <Form.Item
            label={'所属管理区域'}
            name={'regionId'}
            style={{ marginBottom: '12px' }}
            required
          >
            <RegionSelect
              form={checkForm}
              columnId={'regionId'}
              columnName={'regionName'}
              readonly={true}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mfrName'}>
          <Form.Item
            label={'生产厂家'}
            name={'mfrName'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择生产厂家' }]}
          >
            {/*<Input disabled placeholder={'请输入生产厂家'} />*/}
            <MfrSelect form={checkForm} columnId={'mfrId'} columnName={'mfrName'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'resTypeName'}>
          <Form.Item
            label={'规格型号'}
            name={'eqpModelNo'}
            style={{ marginBottom: '12px' }}
            rules={[
              {
                required: true,
                validateTrigger: 'change',
                message: '请选择规格型号',
              },
            ]}
          >
            <EqpModelSelect form={checkForm} columnId={'eqpModelId'} columnName={'eqpModelNo'} />
          </Form.Item>
        </Col>,
        <Col hidden={!checkFormResult} span={8} key={'eqpSequence'}>
          <Form.Item
            label={'设备序列号'}
            name={'eqpSequence'}
            style={{ marginBottom: '12px' }}
            rules={[
              { required: checkFormResult, validateTrigger: 'blur', message: '请输入设备序列号' },
            ]}
          >
            <Input placeholder={'请输入设备序列号'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mntStateId'}>
          <Form.Item
            label={'维护状态'}
            name={'mntStateId'}
            style={{ marginBottom: '12px' }}
            required
          >
            <Select
              placeholder={'请选择维护状态'}
              disabled
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.mntState || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'swVer'}>
          <Form.Item
            label={'软件版本'}
            name={'swVer'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'blur', message: '请输入软件版本' }]}
          >
            <Input placeholder={'请输入软件版本'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'useTime'}>
          <Form.Item
            label={'入网时间'}
            name={'useTime'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择入网时间' }]}
          >
            <DatePickerAsString
              showTime
              placeholder={'请选择入网时间'}
              allowClear
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'type'}>
          <Form.Item label={'资源类型'} style={{ marginBottom: '12px' }} required>
            <Input disabled value={resourceType} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'propCharId'}>
          <Form.Item
            required
            label={'产权性质'}
            name={'propCharId'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择产权性质' }]}
          >
            <Select
              placeholder={'请选择产权性质'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.propChar || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'propertyBelong'}>
          <Form.Item
            label={'产权归属'}
            name={'propertyBelong'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择产权归属' }]}
          >
            <Select
              placeholder={'请选择产权归属'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.propertyBelong || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'buildModelId'}>
          <Form.Item
            label={'建设模式'}
            name={'buildModelId'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择建设模式' }]}
          >
            <Select
              placeholder={'请选择建设模式'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.buildModel || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mntLoginName'}>
          <Form.Item
            label={'包机人'}
            name={'mntLoginName'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择包机人' }]}
          >
            {/*<Input placeholder={'请输入包机人'} allowClear />*/}
            <MntManSelect form={checkForm} columnId={'mntMan'} columnName={'mntLoginName'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'isVirtual'}>
          <Form.Item
            label={'是否虚拟资源'}
            name={'isVirtual'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'change', message: '请选择是否虚拟资源' }]}
          >
            <Select
              placeholder={'请选择是否虚拟资源'}
              allowClear
              options={[
                { label: '是', value: '1', key: 1 },
                { label: '否', value: '0', key: 0 },
              ]}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'manageIpaddress'}>
          <Form.Item
            required
            label={'管理IP地址'}
            name={'manageIpaddress'}
            style={{ marginBottom: '12px' }}
            rules={[
              { required: true, message: '请输入管理IP地址' },
              {
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback();
                    return;
                  }
                  if (validateIP(value)) {
                    callback();
                  } else {
                    callback('IP地址格式错误');
                  }
                },
              },
            ]}
          >
            <Input disabled={checkFormResult} allowClear placeholder={'请输入管理IP地址'} />
          </Form.Item>
        </Col>,
      ];
    } else if (resourceType == '板卡') {
      return [
        <Col span={8} key={'cardName'}>
          <Form.Item
            label={'板卡名称'}
            name={'cardName'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'blur', message: '请输入板卡名称' }]}
          >
            <Input placeholder={'请输入板卡名称'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'cardNo'}>
          <Form.Item
            label={'板卡编号'}
            name={'cardNo'}
            style={{ marginBottom: '12px' }}
            rules={[{ required: true, validateTrigger: 'blur', message: '请输入板卡编号' }]}
          >
            <Input placeholder={'请输入板卡编号'} />
          </Form.Item>
        </Col>,
        <Col span={8} hidden={!checkFormResult} key={'cardModelId'}>
          <Form.Item
            label={'板卡型号'}
            name={'cardModel'}
            style={{ marginBottom: '12px' }}
            rules={[
              { required: checkFormResult, validateTrigger: 'change', message: '请选择板卡型号' },
            ]}
          >
            <CardModelSelect form={checkForm} columnId={'cardModelId'} columnName={'cardModel'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mntStateId'}>
          <Form.Item
            label={'维护状态'}
            name={'mntStateId'}
            style={{ marginBottom: '12px' }}
            required
          >
            <Select
              placeholder={'请选择维护状态'}
              disabled
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.mntState || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'oprStateId'}>
          <Form.Item
            label={'业务状态'}
            name={'oprStateId'}
            style={{ marginBottom: '12px' }}
            required
          >
            <Select
              placeholder={'请选择业务状态'}
              disabled
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.oprState || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'cardInstallDate'}>
          <Form.Item
            label={'入网时间'}
            name={'installDate'}
            style={{ marginBottom: '12px' }}
            required
          >
            <DatePickerAsString
              showTime
              placeholder={'请选择入网时间'}
              allowClear
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'type'}>
          <Form.Item label={'资源类型'} style={{ marginBottom: '12px' }} required>
            <Input disabled value={resourceType} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'superResTypeName'}>
          <Form.Item label={'所属设备类型'} style={{ marginBottom: '12px' }} required>
            <Input disabled value={'OLT'} placeholder={'请输入所属设备类型'} />
          </Form.Item>
        </Col>,
      ];
    } else {
      if (portType == 'ipnetwork') {
        return [
          <Col span={8} key={'portName'}>
            <Form.Item
              label={'端口名称'}
              name={'portName'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'blur', message: '请输入端口名称' }]}
            >
              <Input placeholder={'请输入端口名称'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portNo'}>
            <Form.Item
              label={'端口编号'}
              name={'portNo'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'blur', message: '请输入端口编号' }]}
            >
              <Input placeholder={'请输入端口编号'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'position'}>
            <Form.Item
              label={'端口序号'}
              name={'position'}
              style={{ marginBottom: '12px' }}
              required
            >
              <InputNumber disabled={checkFormResult} placeholder={'请输入端口序号'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'emsOrigResId'}>
            <Form.Item
              label={'端口网管标识'}
              name={'emsOrigResId'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Input disabled placeholder={'请输入端口网管标识'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'emsOrigResName'}>
            <Form.Item
              label={'端口网管名称'}
              name={'emsOrigResName'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Input disabled placeholder={'请输入端口网管名称'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'superResName'}>
            <Form.Item
              label={'所属设备名称'}
              name={'superResName'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Input disabled placeholder={'请输入所属设备类型'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portTypeId'} hidden={!checkFormResult}>
            <Form.Item
              label={'端口光电类型'}
              name={'portTypeId'}
              style={{ marginBottom: '12px' }}
              rules={[
                {
                  required: checkFormResult,
                  validateTrigger: 'change',
                  message: '请选择端口光电类型',
                },
              ]}
            >
              <Select
                placeholder={'请选择端口光电类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portTypeId || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portType'}>
            <Form.Item
              label={'端口类型'}
              name={'portType'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'change', message: '请选择端口类型' }]}
            >
              <Select
                placeholder={'请选择端口类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portType || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portRate'} hidden={!checkFormResult}>
            <Form.Item
              label={'端口速率'}
              name={'portRate'}
              style={{ marginBottom: '12px' }}
              rules={[
                { required: checkFormResult, validateTrigger: 'change', message: '请选择端口速率' },
              ]}
            >
              <Select
                placeholder={'请选择端口速率'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portRate || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'oprStateId'}>
            <Form.Item
              label={'端口业务状态'}
              name={'oprStateId'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Select
                disabled
                placeholder={'请选择端口业务状态'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.oprState || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'mntStateId'}>
            <Form.Item
              label={'维护状态'}
              name={'mntStateId'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Select
                disabled
                placeholder={'请选择维护状态'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.mntState || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'superResTypeName'}>
            <Form.Item label={'所属设备类型'} style={{ marginBottom: '12px' }} required>
              <Input disabled value={'OLT'} placeholder={'请输入所属设备类型'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'type'}>
            <Form.Item label={'资源类型'} style={{ marginBottom: '12px' }} required>
              <Input disabled value={'IP网端口'} />
            </Form.Item>
          </Col>,
        ];
      } else {
        return [
          <Col span={8} key={'type'}>
            <Form.Item label={'资源类型'} style={{ marginBottom: '12px' }} required>
              <Input disabled value={'PON端口'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portName'}>
            <Form.Item
              label={'端口名称'}
              name={'portName'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'blur', message: '请输入端口名称' }]}
            >
              <Input placeholder={'请输入端口名称'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portNo'}>
            <Form.Item
              label={'端口编号'}
              name={'portNo'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'blur', message: '请输入端口编号' }]}
            >
              <Input placeholder={'请输入端口编号'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'position'}>
            <Form.Item
              label={'端口序号'}
              name={'position'}
              style={{ marginBottom: '12px' }}
              rules={[
                {
                  required: true,
                  type: 'number',
                  validateTrigger: ['change', 'blur'],
                  message: '请输入端口序号',
                },
              ]}
            >
              <InputNumber placeholder={'请输入端口序号'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portType'}>
            <Form.Item
              label={'端口类型'}
              name={'portType'}
              rules={[{ required: true, validateTrigger: 'change', message: '请选择端口类型' }]}
              style={{ marginBottom: '12px' }}
            >
              <Select
                placeholder={'请选择端口类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portType || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portTypeId'}>
            <Form.Item
              label={'端口光电类型'}
              name={'portTypeId'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'change', message: '请选择端口光电类型' }]}
            >
              {/*<Input placeholder={'请选择端口光电类型'} />*/}
              <Select
                placeholder={'请选择端口光电类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portTypeId || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'oprStateId'}>
            <Form.Item
              label={'端口业务状态'}
              name={'oprStateId'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'change', message: '请选择端口业务状态' }]}
            >
              <Select
                placeholder={'请选择端口业务状态'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.oprState || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'mntStateId'}>
            <Form.Item
              label={'维护状态'}
              name={'mntStateId'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Select
                placeholder={'请选择维护状态'}
                disabled
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.mntState || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'cardName'}>
            <Form.Item
              label={'所属板卡'}
              name={'cardName'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Input disabled placeholder={'请输入所属板卡'} />
            </Form.Item>
          </Col>,
          // <Col span={8} key={'superResTypeName'}>
          //   <Form.Item label={'所属设备类型'} style={{ marginBottom: '12px' }}>
          //     <Input disabled value={'OLT'} placeholder={'请输入所属设备类型'} />
          //   </Form.Item>
          // </Col>,
          <Col span={8} key={'superResName'}>
            <Form.Item
              label={'所属设备名称'}
              name={'superResName'}
              style={{ marginBottom: '12px' }}
              required
            >
              <Input disabled placeholder={'请输入所属设备类型'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'technicalMode'}>
            <Form.Item
              label={'技术制式'}
              name={'technicalMode'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: true, validateTrigger: 'change', message: '请选择技术制式' }]}
            >
              <Select
                placeholder={'请选择技术制式'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.technicalMode || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'superResTypeName'}>
            <Form.Item label={'所属设备类型'} style={{ marginBottom: '12px' }} required>
              <Input disabled value={'OLT'} placeholder={'请输入所属设备类型'} />
            </Form.Item>
          </Col>,
        ];
      }
    }
  };

  /**
   * 选填字段列表
   */
  const getOptionalFormItems = () => {
    if (resourceType == 'OLT') {
      // OLT设备的表单字段信息
      return [
        <Col span={8} key={'alias'}>
          <Form.Item label={'设备别名'} name={'alias'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入设备别名'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'nmsOrigResId'}>
          <Form.Item
            label={'设备采集前置标识'}
            name={'nmsOrigResId'}
            style={{ marginBottom: '12px' }}
          >
            <Input disabled placeholder={'请输入设备采集前置标识'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'nmsOrigResName'}>
          <Form.Item
            label={'设备采集前置名称'}
            name={'nmsOrigResName'}
            style={{ marginBottom: '12px' }}
          >
            <Input disabled placeholder={'请输入设备采集前置名称'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'emsOrigResId'}>
          <Form.Item label={'设备网管标识'} name={'emsOrigResId'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入设备网管标识'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'emsOrigResName'}>
          <Form.Item
            label={'设备网管名称'}
            name={'emsOrigResName'}
            style={{ marginBottom: '12px' }}
          >
            <Input disabled placeholder={'请输入设备网管名称'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'lifeCycle'}>
          <Form.Item label={'资源生命周期'} name={'lifeCycle'} style={{ marginBottom: '12px' }}>
            <Select
              disabled
              placeholder={'请选择资源生命周期'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.lifeCycle || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'pConfirmStatus'}>
          <Form.Item label={'确认状态'} name={'pConfirmStatus'} style={{ marginBottom: '12px' }}>
            <Select
              placeholder={'请选择确认状态'}
              disabled
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.pConfirmStatus || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'resLockedState'}>
          <Form.Item
            label={'资源封锁状态'}
            name={'resLockedState'}
            style={{ marginBottom: '12px' }}
          >
            <Select
              placeholder={'请选择资源封锁状态'}
              disabled
              allowClear
              options={[
                { label: '是', value: '1', key: 1 },
                { label: '否', value: '0', key: 0 },
              ]}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'hdweVer'}>
          <Form.Item label={'硬件版本'} name={'hdweVer'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入硬件版本'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mntTypeId'}>
          <Form.Item label={'维护方式'} name={'mntTypeId'} style={{ marginBottom: '12px' }}>
            <Select
              placeholder={'请选择维护方式'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.mntType || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'notes'}>
          <Form.Item label={'备注'} name={'notes'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入备注'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'createOp'}>
          <Form.Item label={'创建人'} name={'createOp'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'创建人'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'createDate'}>
          <Form.Item label={'创建时间'} name={'createDate'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'创建时间'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'modifyOp'}>
          <Form.Item label={'修改人'} name={'modifyOp'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'修改人'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'modifyDate'}>
          <Form.Item label={'修改时间'} name={'modifyDate'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'修改时间'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'coopModelId'}>
          <Form.Item
            label={'合作模式'}
            name={'coopModelId'}
            style={{ marginBottom: '12px' }}
            rules={[
              {
                validateTrigger: 'change',
                validator: (rule, value, callback) => {
                  const buildModeValue = checkForm.getFieldValue('buildModelId');
                  if (buildModeValue && buildModeValue != '1486627' && !value) {
                    callback('请选择合作模式');
                  } else {
                    callback();
                  }
                },
              },
            ]}
          >
            <Select
              placeholder={'请选择合作模式'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.coopModel || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'powerInfo'}>
          <Form.Item label={'额定功率（W）'} name={'powerInfo'} style={{ marginBottom: '12px' }}>
            <InputNumber placeholder={'额定功率（W）'} style={{ width: '100%' }} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mntManName'}>
          <Form.Item label={'包机人姓名'} name={'mntManName'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'包机人姓名'} style={{ width: '100%' }} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'mntManPhone'}>
          <Form.Item label={'包机人电话'} name={'mntManPhone'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'包机人电话'} style={{ width: '100%' }} type={'number'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'propertyBelongLimit'}>
          <Form.Item
            label={'产权归属时限'}
            name={'propertyBelongLimit'}
            style={{ marginBottom: '12px' }}
            rules={[
              {
                validateTrigger: 'change',
                validator: (rule, value, callback) => {
                  const coopModelValue = checkForm.getFieldValue('coopModelId');
                  if (coopModelValue == '1486653' && !value) {
                    callback('合作模式为BOT时,产权归属时限必填');
                  } else {
                    callback();
                  }
                },
              },
            ]}
          >
            <DatePickerAsString
              showTime
              placeholder={'请选择产权归属时限'}
              allowClear
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>,
      ];
    } else if (resourceType == '板卡') {
      return [
        <Col span={8} key={'alias'}>
          <Form.Item label={'板卡别名'} name={'alias'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入板卡别名'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'hdweVer'}>
          <Form.Item label={'硬件版本号'} name={'hdweVer'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入硬件版本号'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'swVer'}>
          <Form.Item label={'软件版本号'} name={'swVer'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入软件版本号'} allowClear />
          </Form.Item>
        </Col>,

        <Col span={8} key={'cardTypeId'}>
          <Form.Item label={'板卡类型'} name={'cardTypeId'} style={{ marginBottom: '12px' }}>
            <CardTypeSelect form={checkForm} columnId={'cardTypeId'} columnName={'cardType'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'cardSerialNo'}>
          <Form.Item label={'板卡序列号'} name={'cardSerialNo'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入板卡序列号'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'specialityId'}>
          <Form.Item label={'专业类型'} name={'specialityId'} style={{ marginBottom: '12px' }}>
            <Select
              placeholder={'请选择专业类型'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.speciality || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'lifeCycle'}>
          <Form.Item label={'资源生命周期'} name={'lifeCycle'} style={{ marginBottom: '12px' }}>
            <Select
              placeholder={'请选择资源生命周期'}
              disabled
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.lifeCycle || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'nmsOrigResId'}>
          <Form.Item
            label={'板卡采集前置标识'}
            name={'nmsOrigResId'}
            style={{ marginBottom: '12px' }}
          >
            <Input disabled placeholder={'请输入板卡采集前置标识'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'nmsOrigResName'}>
          <Form.Item
            label={'板卡采集前置名称'}
            name={'nmsOrigResName'}
            style={{ marginBottom: '12px' }}
          >
            <Input disabled placeholder={'请输入板卡采集前置名称'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'emsOrigResId'}>
          <Form.Item label={'板卡网管标识'} name={'emsOrigResId'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入板卡网管标识'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'emsOrigResName'}>
          <Form.Item
            label={'板卡网管名称'}
            name={'emsOrigResName'}
            style={{ marginBottom: '12px' }}
          >
            <Input disabled placeholder={'请输入板卡网管名称'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'rackNo'}>
          <Form.Item label={'机柜号'} name={'rackNo'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入机柜号'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'shelfNo'}>
          <Form.Item label={'机框号'} name={'shelfNo'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入机框号'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'slotNo'}>
          <Form.Item label={'槽位号'} name={'slotNo'} style={{ marginBottom: '12px' }}>
            <Input disabled={checkFormResult} placeholder={'请输入槽位号'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'plmn'}>
          <Form.Item label={'承建方PLMN'} name={'plmn'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入承建方PLMN'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'gnodebId'}>
          <Form.Item label={'所属GNODEB ID'} name={'gnodebId'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入所属GNODEB ID'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'pSyncId'}>
          <Form.Item label={'同步标识ID'} name={'pSyncId'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入同步标识ID'} />
          </Form.Item>
        </Col>,
        <Col span={8} key={'datacollectTime'}>
          <Form.Item
            label={'数据采集时间'}
            name={'datacollectTime'}
            style={{ marginBottom: '12px' }}
          >
            <DatePickerAsString
              disabled
              showTime
              placeholder={'请选择数据采集时间'}
              allowClear
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'notes'}>
          <Form.Item label={'备注'} name={'notes'} style={{ marginBottom: '12px' }}>
            <Input placeholder={'请输入备注'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'createOp'}>
          <Form.Item label={'创建人'} name={'createOp'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'创建人'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'createDate'}>
          <Form.Item label={'创建时间'} name={'createDate'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'创建时间'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'modifyOp'}>
          <Form.Item label={'修改人'} name={'modifyOp'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'修改人'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'modifyDate'}>
          <Form.Item label={'修改时间'} name={'modifyDate'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'修改时间'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'resLockedState'}>
          <Form.Item
            label={'资源封锁状态'}
            name={'resLockedState'}
            style={{ marginBottom: '12px' }}
          >
            <Select
              placeholder={'请选择资源封锁状态'}
              disabled
              allowClear
              options={[
                { label: '是', value: '1', key: 1 },
                { label: '否', value: '0', key: 0 },
              ]}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'pConfirmStatus'}>
          <Form.Item label={'确认状态'} name={'pConfirmStatus'} style={{ marginBottom: '12px' }}>
            <Select
              placeholder={'请选择确认状态'}
              disabled
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.pConfirmStatus || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'collectCardModelId'}>
          <Form.Item
            label={'采集板卡型号'}
            name={'collectCardModelId'}
            style={{ marginBottom: '12px' }}
          >
            <CardModelSelect
              form={checkForm}
              readonly
              columnId={'collectCardModelId'}
              columnName={'collectCardModel'}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'belongNetwork'}>
          <Form.Item label={'所属网络'} name={'belongNetwork'} style={{ marginBottom: '12px' }}>
            <Select
              disabled
              placeholder={'请选择所属网络'}
              allowClear
              showSearch
              optionFilterProp="label"
              options={(enumMap.belongNetwork || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          </Form.Item>
        </Col>,
        <Col span={8} key={'dataSource'}>
          <Form.Item label={'数据来源'} name={'dataSource'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入数据来源'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'workOrderNo'}>
          <Form.Item label={'入网工单'} name={'workOrderNo'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入入网工单'} allowClear />
          </Form.Item>
        </Col>,
        <Col span={8} key={'superResName'}>
          <Form.Item label={'所属设备名称'} name={'superResName'} style={{ marginBottom: '12px' }}>
            <Input disabled placeholder={'请输入所属设备名称'} />
          </Form.Item>
        </Col>,
      ];
    } else {
      if (portType == 'ipnetwork') {
        return [
          <Col span={8} key={'alias'}>
            <Form.Item label={'端口别名'} name={'alias'} style={{ marginBottom: '12px' }}>
              <Input placeholder={'请输入端口别名'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'cardName'}>
            <Form.Item label={'所属板卡'} name={'cardName'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'请输入所属板卡'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portProtocalType'}>
            <Form.Item
              label={'端口协议类型'}
              name={'portProtocalType'}
              style={{ marginBottom: '12px' }}
            >
              <Select
                placeholder={'请选择协议类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portProtocalType || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portPurpose'}>
            <Form.Item label={'端口用途'} name={'portPurpose'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'请输入端口用途'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'machineChar'}>
            <Form.Item label={'机械特性'} name={'machineChar'} style={{ marginBottom: '12px' }}>
              <Select
                placeholder={'请选择机械特性'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.machineChar || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portWavelength'}>
            <Form.Item label={'端口波长'} name={'portWavelength'} style={{ marginBottom: '12px' }}>
              <Select
                placeholder={'请选择端口波长'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portWavelength || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'superPortId'}>
            <Form.Item label={'上级端口id'} name={'superPortId'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'请输入上级端口id'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'protocolStatus'}>
            <Form.Item
              label={'端口管理状态'}
              name={'protocolStatus'}
              style={{ marginBottom: '12px' }}
            >
              <Select
                placeholder={'请选择端口管理状态'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.protocolStatus || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portStatus'}>
            <Form.Item label={'端口运行状态'} name={'portStatus'} style={{ marginBottom: '12px' }}>
              <Select
                placeholder={'请选择端口运行状态'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portStatus || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'notes'}>
            <Form.Item label={'备注'} name={'notes'} style={{ marginBottom: '12px' }}>
              <Input placeholder={'请输入备注'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'ipAddress'}>
            <Form.Item
              label={'IP地址'}
              name={'ipAddress'}
              style={{ marginBottom: '12px' }}
              rules={[
                {
                  validator: (rule, value, callback) => {
                    if (!value) {
                      callback();
                      return;
                    }
                    if (validateIP(value)) {
                      callback();
                    } else {
                      callback('IP地址格式错误');
                    }
                  },
                },
              ]}
            >
              <Input placeholder={'请输入IP地址'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'subnetMask'}>
            <Form.Item
              label={'IP地址掩码'}
              name={'subnetMask'}
              style={{ marginBottom: '12px' }}
              rules={[
                {
                  validator: (rule, value, callback) => {
                    if (!value) {
                      callback();
                      return;
                    }
                    if (validateIP(value)) {
                      callback();
                    } else {
                      callback('IP地址格式错误');
                    }
                  },
                },
              ]}
            >
              <Input placeholder={'请输入IP地址掩码'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portLevel'}>
            <Form.Item label={'端口级别'} name={'portLevel'} style={{ marginBottom: '12px' }}>
              <Select
                placeholder={'请选择端口级别'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portLevel || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'nmsOrigResId'}>
            <Form.Item
              label={'端口采集前置标识'}
              name={'nmsOrigResId'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入端口采集前置标识'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'nmsOrigResName'}>
            <Form.Item
              label={'端口采集前置名称'}
              name={'nmsOrigResName'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入端口采集前置名称'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'datacollectTime'}>
            <Form.Item
              label={'数据采集时间'}
              name={'datacollectTime'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入数据采集时间'} />
            </Form.Item>
          </Col>,
          // <Col span={8} key={'portwlxh'}>
          //   <Form.Item label={'端口物理序号'} name={'portwlxh'} style={{ marginBottom: '12px' }}>
          //     <Input disabled placeholder={'请输入端口物理序号'} />
          //   </Form.Item>
          // </Col>,
          <Col span={8} key={'belongNetwork'}>
            <Form.Item label={'所属网络'} name={'belongNetwork'} style={{ marginBottom: '12px' }}>
              <Select
                disabled
                placeholder={'请选择所属网络'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.belongNetwork || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'specialityId'}>
            <Form.Item label={'专业类型'} name={'specialityId'} style={{ marginBottom: '12px' }}>
              <Select
                disabled
                placeholder={'请选择专业类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.speciality || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'dataSource'}>
            <Form.Item label={'数据来源'} name={'dataSource'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'请输入数据来源'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'pSyncId'}>
            <Form.Item label={'同步标识ID'} name={'pSyncId'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'请输入同步标识ID'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'datacollectState'}>
            <Form.Item
              label={'端口采集状态'}
              name={'datacollectState'}
              style={{ marginBottom: '12px' }}
            >
              <Select
                placeholder={'请选择端口采集状态'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.datacollectState || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
        ];
      } else {
        return [
          <Col span={8} key={'alias'}>
            <Form.Item label={'端口别名'} name={'alias'} style={{ marginBottom: '12px' }}>
              <Input placeholder={'请输入端口别名'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'portRate'}>
            <Form.Item
              label={'端口速率'}
              name={'portRate'}
              style={{ marginBottom: '12px' }}
              rules={[{ required: false, validateTrigger: 'change', message: '请选择端口速率' }]}
            >
              <Select
                placeholder={'请选择端口速率'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.portRate || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'nmsOrigResId'}>
            <Form.Item
              label={'端口采集前置标识'}
              name={'nmsOrigResId'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入端口采集前置标识'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'nmsOrigResName'}>
            <Form.Item
              label={'端口采集前置名称'}
              name={'nmsOrigResName'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入端口采集前置名称'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'emsOrigResId'}>
            <Form.Item
              label={'端口网管标识'}
              name={'emsOrigResId'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入端口网管标识'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'emsOrigResName'}>
            <Form.Item
              label={'端口网管名称'}
              name={'emsOrigResName'}
              style={{ marginBottom: '12px' }}
            >
              <Input disabled placeholder={'请输入端口网管名称'} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'notes'}>
            <Form.Item label={'备注'} name={'notes'} style={{ marginBottom: '12px' }}>
              <Input placeholder={'请输入备注'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'res'}>
            <Form.Item label={'业务开通能力'} name={'res'} style={{ marginBottom: '12px' }}>
              <Select
                placeholder={'请选择端口采集状态'}
                allowClear
                options={[
                  { label: '无能力', value: '0', key: '0' },
                  { label: '有能力', value: '1', key: '1' },
                ]}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'pstnNum'}>
            <Form.Item label={'宽带用户数'} name={'pstnNum'} style={{ marginBottom: '12px' }}>
              <InputNumber placeholder={'宽带用户数'} min={0} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'iptvNum'}>
            <Form.Item label={'IPTV用户数'} name={'iptvNum'} style={{ marginBottom: '12px' }}>
              <InputNumber placeholder={'IPTV用户数'} min={0} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'isdnNum'}>
            <Form.Item label={'语音用户数'} name={'isdnNum'} style={{ marginBottom: '12px' }}>
              <InputNumber placeholder={'语音用户数'} min={0} />
            </Form.Item>
          </Col>,
          <Col span={8} key={'createOp'}>
            <Form.Item label={'创建人'} name={'createOp'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'创建人'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'createDate'}>
            <Form.Item label={'创建时间'} name={'createDate'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'创建时间'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'modifyOp'}>
            <Form.Item label={'修改人'} name={'modifyOp'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'修改人'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'modifyDate'}>
            <Form.Item label={'修改时间'} name={'modifyDate'} style={{ marginBottom: '12px' }}>
              <Input disabled placeholder={'修改时间'} allowClear />
            </Form.Item>
          </Col>,
          <Col span={8} key={'belongNetwork'}>
            <Form.Item label={'所属网络'} name={'belongNetwork'} style={{ marginBottom: '12px' }}>
              <Select
                disabled
                placeholder={'请选择所属网络'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.belongNetwork || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
          <Col span={8} key={'specialityId'}>
            <Form.Item label={'专业类型'} name={'specialityId'} style={{ marginBottom: '12px' }}>
              <Select
                disabled
                placeholder={'请选择专业类型'}
                allowClear
                showSearch
                optionFilterProp="label"
                options={(enumMap.speciality || []).map((item: any) => ({
                  label: item.dictName,
                  value: item.dictCode,
                  key: item.dictCode,
                }))}
              />
            </Form.Item>
          </Col>,
        ];
      }
    }
  };

  return (
    <Space>
      <Modal
        title={'资源核对'}
        width={'75%'}
        open={open}
        onOk={() => submit()}
        onCancel={() => {
          setOpen(false)
          // setActionForm({
          //   operator: 'pass',
          //   submitToProv: true,
          //   participantIds: [],
          // })
          updateActionForm('participantIds', [])
          updateActionForm('note', "");
          updateActionForm('remark', "")
          if (fileList && fileList.length > 0) {
            fileList.forEach((file) => {
              resetHandleFileRemove(file);
            });
            setFileList([]);
          }
        }}
        okButtonProps={{
          loading: okLoading,
          // disabled: actionForm.operator == 'pass' && !checkResult,
        }}
        okText={'提交'}
      >
        <Space direction="vertical" size="small" style={{ display: 'flex' }}>
          <Card
            title={netType == 'ranAccess' ? '入网资源核对记录' : '退网资源核对记录'}
            extra={
              <Button type={'primary'} loading={checkLoading} onClick={autoCheckAndQuery}>
                自动核对
              </Button>
            }
          >
            <ProTable
              rowKey="woResId"
              search={false}
              columns={columns}
              tableAlertRender={false}
              toolBarRender={false}
              expandable={{ defaultExpandAllRows: false }}
              dataSource={tableData}
              loading={checkLoading}
              pagination={false}
              scroll={{ y: 'calc(100vh - 500px)' }}
            />
          </Card>
          {OperatorDescriptions}
        </Space>

        {/** 资源核对详情 */}
        <Modal
          title={modalTitle}
          width={'75%'}
          open={checkModalVisible}
          onOk={() => manualCheck()}
          onCancel={() => setCheckModalVisible(false)}
          okButtonProps={{ loading: checkLoading }}
          closable={true}
        >
          <Form form={checkForm} labelCol={{ span: 8, offset: 1 }}>
            <Tabs
              type="card"
              activeKey="required-attributes"
              style={{ margin: '10px 0' }}
              items={[
                {
                  key: 'required-attributes',
                  label: '必填属性',
                  children: <Row>{getRequiredFormItems()}</Row>,
                },
              ]}
            />
            <Tabs
              type="card"
              activeKey="optional-attributes"
              style={{ margin: '10px 0' }}
              items={[
                {
                  key: 'optional-attributes',
                  label: '选填属性',
                  children: <Row>{getOptionalFormItems()}</Row>,
                },
              ]}
            />

            {/*<Row>{getResourceFormItems()}</Row>*/}
          </Form>
        </Modal>

        {/** 资源失败详情 */}
        <Modal
          title="失败详情列表"
          width={'75%'}
          open={failModalVisible}
          onCancel={() => setFailModalVisible(false)}
          okButtonProps={{ hidden: true }}
          cancelButtonProps={{ hidden: true }}
          closable={true}
        >
          <ProTable
            rowKey="id"
            defaultSize={'small'}
            toolBarRender={false}
            tableAlertRender={false}
            search={false}
            columns={[
              {
                title: '资源类型',
                dataIndex: 'resType',
                align: 'center',
              },
              {
                title: '资源id',
                dataIndex: 'resId',
                align: 'center',
              },
              {
                title: '资源名称',
                dataIndex: 'resName',
                align: 'center',
              },
              {
                title: '描述说明',
                dataIndex: 'checkMess',
                align: 'center',
              },
            ]}
            dataSource={failList}
            pagination={false}
          />
        </Modal>
      </Modal>
      {contextHolder}
    </Space>
  );
};
