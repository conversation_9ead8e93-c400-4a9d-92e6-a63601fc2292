import { useLocation } from 'umi';
import NoAuth from './no-auth';
// 接入网详情
import AccessnetDetail from './accessnet/AccessnetDetail';
import AccessnetDetailCutGrafting from './accessnetCutGrafting/AccessnetDetail';
import TransfernetDetail from './transfernet/TransfernetDetail';
import styles from './index.less';
import { useEffect, useRef } from 'react';

export default () => {
  const { query } = useLocation() as any;
  const queryData = useRef<any>(query);
  // const [netType, setNetType] = useState<string>('ranAccess');

  // 前阶段切换tab导致query丢失使用组件缓存临时解决下
  if (query.woId || !queryData.current.woId) {
    queryData.current = query;
    // history.location.query = query;
  }
  // 正常需要根据id查询工单信息，这里暂时通过参数获取
  const {
    woId,
    sheetNo,
    speciality,
    status,
    sheetTitle,
    activityCode,
    activityName,
    processInstId,
    netType,
  } = queryData.current;

  useEffect(() => {
    // 查询工单信息
  }, [woId]);

  if (!woId || !speciality) {
    return <NoAuth />;
  }

  // 根据不同的专业跳转到不同的详情页面
  switch (speciality) {
    case 'accessnet':
      /*接入网专业*/
      return (
        <>
          <AccessnetDetail
            className={styles.accessnet}
            woId={woId}
            sheetNo={sheetNo}
            sheetTitle={sheetTitle}
            activityCode={activityCode}
            activityName={activityName}
            processInstId={processInstId}
            speciality={speciality}
            netType={netType || 'ranAccess'}
            readonly={status == 'done'}
          />
        </>
      );
    case 'transfernet': {
      /*传输网专业*/
      return (
        <TransfernetDetail
          className={styles.accessnet}
          woId={woId}
          sheetNo={sheetNo}
          sheetTitle={sheetTitle}
          activityCode={activityCode}
          activityName={activityName}
          processInstId={processInstId}
          speciality={speciality}
          netType={netType}
          readonly={status == 'done'}
        />
      );
    }
    case 'accessnetCutOver': {
      /*接入网-割接专业*/
      return (
        <AccessnetDetailCutGrafting
          className={styles.accessnet}
          woId={woId}
          sheetNo={sheetNo}
          sheetTitle={sheetTitle}
          activityCode={activityCode}
          activityName={activityName}
          processInstId={processInstId}
          speciality={speciality}
          netType={netType || 'ranAccess'}
          readonly={status == 'done'}
        />
      );
    }
  }
  return <NoAuth />;
};
