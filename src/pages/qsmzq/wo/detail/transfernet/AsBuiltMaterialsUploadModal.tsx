import { Button, Descriptions, Input, message, Modal, Radio, Select, Upload } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { useEffect, useState } from 'react';
import type { TransferNetType, WorkOrderRecord } from '@/pages/qsmzq/data.d';
import type { FlowLogRecord } from '../data.d';
import moment from 'moment';
import { completeTask, getAuditParticipants, getAuditProvinceUsers } from '@/pages/qsmzq/service';
import {
  buildParticipantOptions,
  buildWorkFlowData,
  getNextParticipants,
  getParticipantIds,
} from '@/pages/qsmzq/utils/WoUtils';
import type { Participant } from '@/pages/qsmzq/data.d';
import ParticipantSelect from '@/pages/qsmzq/components/ParticipantSelect';
import { useModel } from '@@/plugin-model/useModel';
import { UploadOutlined } from '@ant-design/icons';
import { getStore } from '@/utils/framework/store';
import { attachList, deleteAttach, downloadAttach } from '@/pages/qsmzq/wo/detail/service';
import type { WorkOrderDetail } from '@/pages/qsmzq/data.d';
interface ComponentProps {
  open: boolean;
  setOpen: (val: boolean) => void;
  onComplete: () => void;
  woRecord: WorkOrderRecord;
  workOrderDetail?: WorkOrderDetail;
  // 以详情（只读方式）方式展示操作记录
  showAsDescriptions?: boolean;
  // 操作记录日志
  flowLogRecord?: FlowLogRecord;
  // 入网或退网
  netType?: TransferNetType | string;
  // 环节编码
  activityCode: string;
  // 附件组id
  attachGroupId?: string;
}

type Operator = 'pass' | 'reject2draft' | 'countersign';

interface SubmitActionForm {
  user?: string;
  checkTime?: string;
  operator: Operator;
  submitToProv: boolean;
  note?: string;
  remark?: string;
  participantIds: string[];
  countersignParticipants: Participant[];
}

export default ({
  open,
  setOpen,
  onComplete,
  woRecord,
  workOrderDetail,
  showAsDescriptions,
  flowLogRecord,
  netType = 'ranAccess',
  activityCode,
  attachGroupId = '',
}: ComponentProps) => {
  const [modal, contextHolder] = Modal.useModal();
  const title = '竣工材料上传';
  // const woId = woRecord.woId;
  const [okLoading, setOkLoading] = useState<boolean>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [actionForm, setActionForm] = useState<SubmitActionForm>({
    user: currentUser?.user.realName,
    checkTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    operator: 'pass',
    submitToProv: true,
    note: '',
    participantIds: [],
    countersignParticipants: [],
  });
  // 审核人下拉
  const [participantOptions, setParticipantOptions] = useState<any[]>([]);
  const updateActionForm = (field: string, val: any) => {
    const form: any = { ...actionForm };
    form[field] = val;
    setActionForm(form);
  };
  const updateActionFormProperties = (props: any) => {
    const form: any = { ...actionForm };
    Object.assign(form, props || {});
    setActionForm(form);
  };
  // 是否省份用户
  const isProvinceUser = workOrderDetail?.category == 'PRO';
  console.log("const isProvinceUser = workOrderDetail?.category == 'PRO'; ", workOrderDetail, showAsDescriptions, isProvinceUser);

  const submit = () => {
    // validate something
    const flowData = buildWorkFlowData(woRecord);
    flowData.note = actionForm.note;
    flowData.remark = actionForm.remark;
    flowData.workFlow.isCountSign = false;
    // 选择的分支
    flowData.operator = actionForm.operator;
    if (actionForm.operator == 'pass') {
      flowData.isProvincialApproval = actionForm.submitToProv;
      const nextParticipants = getNextParticipants(actionForm.participantIds, participantOptions);
      console.log("nextParticipants", nextParticipants);
      // 设置下一步提交人
      if (nextParticipants.length == 0) {
        message.error('请选择处理人');
        return;
      }
      flowData.workFlow.nextParticipant = nextParticipants;
    } else if (actionForm.operator == 'reject2draft') {
    } else {
      // 会签
      flowData.workFlow.isCountSign = true;
      if (actionForm.countersignParticipants.length == 0) {
        message.error('请选择会签主送人');
        return;
      }
      flowData.workFlow.countersignParticipant = actionForm.countersignParticipants;
      flowData.isProvincialApproval = actionForm.submitToProv;
      if (true /*netType == 'ranWithdrawal' || !flowData.isProvincialApproval*/) {
        const nextParticipants = getNextParticipants(actionForm.participantIds, participantOptions);
        // 设置下一步提交人
        if (nextParticipants.length == 0) {
          message.error('请选择审核人');
          return;
        }
        flowData.workFlow.nextParticipant = nextParticipants;
      }
    }

    setOkLoading(true);
    completeTask(netType as string, flowData)
      .then((res) => {
        const { code, msg = '服务端错误' } = res;
        if (code == 200 || code == 2000) {
          message.info('操作成功');
          setOpen(false);
          onComplete();
          // 返回
        } else {
          message.destroy();
          message.error('操作失败: ' + msg);
        }
      })
      .catch((err) => {
        message.error('操作失败: ' + err);
      })
      .finally(() => {
        setOkLoading(false);
      });
  };
  const headers = {
    'x-token': getStore('Token'),
  };
  // 附件列表(可初次通过接口获取)
  const [fileList, setFileList] = useState<UploadFile[]>([
    // {
    //   uid: '-1',
    //   name: '测试.txt',
    //   status: 'done',
    //   // url: '/api/aqsmzq-common/attach/download?attId=1',
    // },
  ]);

  // 上传最大文件大小
  const MAX_FILE_SIZE = 20 * 1024 * 1024;
  const accepts: string[] = [
    '.txt',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
    '.pdf',
    '.zip',
    '.rar',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.heic',
  ];
  const handleBeforeUpload = (file: UploadFile, fl: UploadFile[]) => {
    const uploadedCnt = fileList.filter((f) => f.status == 'done').length;
    if (fl.length > 5 - uploadedCnt) {
      message.destroy();
      if (uploadedCnt > 0) {
        if (uploadedCnt >= 5) {
          message.error(
            `最多支持上传5个附件,已上传${uploadedCnt}个附件,请先删除部分附件再上传新的附件`,
          );
        } else {
          message.error(
            `最多支持上传5个附件,已上传${uploadedCnt}个附件,最多选择${5 - uploadedCnt}个`,
          );
        }
      } else {
        message.error('最多支持上传5个附件');
      }
      return Upload.LIST_IGNORE;
    }
    const size: number = file.size as number;
    if (size > MAX_FILE_SIZE) {
      message.error(`附件[${file.name}]大小超过20MB,不能上传`);
      file.status = 'error';
      file.response = `附件[${file.name}]大小超过20MB,不能上传`;
      return Upload.LIST_IGNORE;
    }
    const name = file.name as string;
    const accept = accepts.find((acc) => name.endsWith(acc));
    if (!accept) {
      message.error(`附件[${file.name}]类型不支持`);
      file.status = 'error';
      file.response = `附件[${file.name}]类型不支持`;
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleFileRemove = (file: UploadFile) => {
    if (file.status != 'done') return true;
    return new Promise<void>(async (resolve, reject) => {
      modal.confirm({
        width: 300,
        centered: true,
        title: '',
        content: '确认删除吗',
        onOk: () => {
          deleteAttach(file.uid).then((res) => {
            const { code, msg = '服务端错误' } = res;
            if (code == 200 || code == 2000) {
              message.info(`附件[${file.name}]已删除`);
              resolve();
            } else {
              message.destroy();
              message.error(msg || '服务端错误');
              reject();
            }
          });
        },
        onCancel: () => {
          reject();
        },
      });
    });
  };

  // 加载附件列表
  const loadAttachList = () => {
    const groupKey = attachGroupId as string;
    if (groupKey) {
      attachList(groupKey).then((res) => {
        const { code, data } = res;
        if (code == 200 || code == 2000) {
          setFileList(
            (data || []).map((item: any) => {
              console.log('file item', item);
              const { attId, attOrigName } = item;
              return {
                uid: attId,
                name: attOrigName,
                status: 'done',
              };
            }),
          );
        } else {
          message.destroy();
          message.error('附件列表加载失败');
        }
      });
    } else {
      console.warn('groupKey is null or empty when load attachList');
    }
  };

  const handleUploadChange = (info: any) => {
    console.log('info', info);
    // 当前上传的附件
    const f = info.file as UploadFile;
    if (f.status != 'removed') {
      if (f && f.response) {
        console.log('f.status', f.status);
        const { code, data } = f.response;
        if (code) {
          if (code != 200 && code != 2000) {
            message.error(`附件[${f.name}]上传失败`);
            f.response = `附件[${f.name}]上传失败`;
            f.status = 'error';
          } else {
            message.info(`附件[${f.name}]上传成功`);
            f.uid = data.id;
          }
        } else {
          f.response = `附件[${f.name}]上传失败: 服务端错误`;
          f.status = 'error';
        }
      }
    }
    const newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const handleFileDownload = (file: UploadFile) => {
    console.log('download file ', file);
    const uid = file.uid;
    downloadAttach(uid, file.name);
  };

  // 处理审核人员列表
  const queryParticipants = (isProvince = false) => {
    const promise: Promise<any> = isProvince
      ? getAuditProvinceUsers({
        processNode: activityCode,
        processName: netType,
        specialty: netType,
        sheetNo: woRecord.sheetNo,
        professionalType: '3',
      })
      : getAuditParticipants({
        processNode: activityCode,
        processName: netType,
        specialty: netType,
        sheetNo: woRecord.sheetNo,
        professionalType: '3',
      });

    // 非readonly下加载审核人列表
    promise
      .then((res) => {
        const { code, data } = res;
        if (code == 200 || code == 2000) {
          const options = (Array.isArray(data) ? data : []).map(
            ({ userName, trueName, showName }) => {
              return {
                key: userName,
                value: userName,
                label: showName,
                trueName,
              };
            },
          );
          console.log("const options = (Array.isArray(data) ? data : []): ", options);
          setParticipantOptions(options);
        }
      })
      .catch(() => { });
  };

  const OperatorDescriptions = (
    <Descriptions
      title={title}
      size="small"
      column={1}
      bordered
      labelStyle={{ width: '30%', textAlign: 'center' }}
    >
      <Descriptions.Item label="审核人">{actionForm.user}</Descriptions.Item>
      <Descriptions.Item label="审核时间">{actionForm.checkTime}</Descriptions.Item>
      <Descriptions.Item label="操作选项">
        <Radio.Group
          disabled={showAsDescriptions}
          value={actionForm.operator}
          onChange={(val) => updateActionForm('operator', val.target.value)}
        >
          <Radio value={'pass'}>通过</Radio>
          <Radio value={'reject2draft'}>驳回</Radio>
          <Radio value={'countersign'}>会签</Radio>
        </Radio.Group>
      </Descriptions.Item>
      {actionForm.operator == 'countersign' && (
        <Descriptions.Item label="主送人">
          <ParticipantSelect
            disabled={showAsDescriptions}
            value={actionForm.countersignParticipants}
            onChange={(val) => updateActionForm('countersignParticipants', val)}
          />
        </Descriptions.Item>
      )}
      {actionForm.operator !== 'reject2draft' && (
        <Descriptions.Item label="提交到省分">

          <Radio.Group
            disabled={showAsDescriptions || isProvinceUser}
            value={actionForm.submitToProv}
            onChange={(val) => {
              queryParticipants(val.target.value);
              updateActionFormProperties({ participantIds: [], submitToProv: val.target.value });
            }}
          >
            <Radio value={true}>是</Radio>
            <Radio value={false}>否</Radio>
          </Radio.Group>
        </Descriptions.Item>
      )}
      {/* 处理审核人员的下拉选项 */}
      {actionForm.operator !== 'reject2draft' && netType == 'trsAccess' && (
        <Descriptions.Item label="审核人">
          <Select
            value={actionForm.participantIds}
            showArrow
            showSearch
            optionFilterProp="label"
            mode={'multiple'}
            disabled={showAsDescriptions}
            placeholder={'请选择处理人'}
            options={participantOptions}
            style={{ width: '100%' }}
            onChange={(val) => updateActionForm('participantIds', val)}
          />
        </Descriptions.Item>
      )}

      <Descriptions.Item label="处理意见">
        <Input
          disabled={showAsDescriptions}
          placeholder={'请填写审核意见'}
          value={actionForm.note}
          onChange={(evt) => updateActionForm('note', evt.target.value)}
        />
      </Descriptions.Item>
      <Descriptions.Item label="附件">
        <Upload
          disabled={!attachGroupId}
          action={`${API_URL}/qsmzq-common/attach/uploadSingle`}
          accept={accepts.join(',')}
          name={'file'}
          headers={headers}
          fileList={fileList}
          showUploadList={{
            showRemoveIcon: !showAsDescriptions,
            showDownloadIcon: true,
          }}
          multiple
          maxCount={5}
          data={{
            groupKey: attachGroupId,
            sheetCreateTime:
              workOrderDetail?.sheetCreateTime || moment().format('YYYY-MM-DD HH:mm:ss'),
          }}
          beforeUpload={handleBeforeUpload}
          onRemove={handleFileRemove}
          onChange={(info) => handleUploadChange(info)}
          onDownload={(file) => handleFileDownload(file)}
        >
          <Button
            disabled={showAsDescriptions || !attachGroupId}
            type={'primary'}
            icon={<UploadOutlined />}
          >
            点击上传
          </Button>

        </Upload>
        <span style={{ display: 'block', fontSize: '12px' }}>
          （支持上传类型.txt, .doc, .docx, .xls, .xlsx, .ppt, .pdf, .pptx,.pdf, .zip, .rar, .jpg, .jpeg, .png, .gif, .heic最大不超过20M）
        </span>
      </Descriptions.Item>
      <Descriptions.Item label="备注说明">
        <Input
          disabled={showAsDescriptions}
          placeholder={'请填写备注说明'}
          value={actionForm.remark}
          allowClear
          onChange={(evt) => updateActionForm('remark', evt.target.value)}
        />
      </Descriptions.Item>
    </Descriptions>
  );

  // 操作表单记录处理
  useEffect(() => {
    if (flowLogRecord) {
      console.log(' flowLogRecord ', flowLogRecord);
      const form = Object.assign(
        { operator: 'pass', submitToProv: true, note: '' },
        {
          user: flowLogRecord.opPersonName,
          checkTime: flowLogRecord.receivedTime,
          operator: flowLogRecord.actionName,
          note: flowLogRecord.processSuggestion,
          remark: flowLogRecord.remark || flowLogRecord.opRequest?.remark,
          submitToProv: !!flowLogRecord.opRequest.isProvincialApproval,
          participantIds: getParticipantIds(flowLogRecord.opRequest?.workFlow?.nextParticipant),
          countersignParticipants: flowLogRecord.opRequest?.workFlow?.countersignParticipant || [],
        },
      );
      // 更新操作表单
      setActionForm(form as SubmitActionForm);
      // 下拉虚拟options
      setParticipantOptions(
        buildParticipantOptions(flowLogRecord.opRequest?.workFlow?.nextParticipant),
      );
      // 加载附件
      loadAttachList();
    }
  }, [flowLogRecord]);

  // 弹出框打开时重置form
  useEffect(() => {
    if (open) {
      // 重置
      setActionForm({
        user: currentUser?.user.realName,
        checkTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        operator: 'pass',
        submitToProv: true,
        note: '',
        participantIds: [],
        countersignParticipants: [],
      });
      // 统一首次查询省份
      updateActionForm('submitToProv', true);
      // 加载人员列表
      queryParticipants(true);
      // 加载附件
      loadAttachList();
    }
  }, [open]);

  if (showAsDescriptions) {
    return OperatorDescriptions;
  }

  return (
    <Modal
      title={title}
      width={'50%'}
      open={open}
      onOk={() => submit()}
      onCancel={() => {
        setOpen(false);
        // console.log("actionForm.operator ================================> ", actionForm.operator);
        // console.log("actionForm.countersignParticipants ================================> ", actionForm.countersignParticipants);
        // console.log("actionForm.submitToProv ================================> ", actionForm.submitToProv);
        // console.log("actionForm.note ================================> ", actionForm.note);
        // console.log("actionForm.remark ================================> ", actionForm.remark);
        actionForm.participantIds = [];
        actionForm.operator = 'pass';
        actionForm.note = '';
        actionForm.remark = '';
        actionForm.countersignParticipants = [];
      }}
      okButtonProps={{ loading: okLoading }}
      okText={'提交'}
    >
      {OperatorDescriptions}

      <div>{contextHolder}</div>
    </Modal>
  );
};
