import {
  Button,
  Card,
  Descriptions,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Space,
  Upload,
} from 'antd';
import { useEffect, useRef, useState } from 'react';
import type { TransferNetType, WorkOrderDetail, WorkOrderRecord } from '@/pages/qsmzq/data.d';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {
  completeTask /*, getAuditParticipants, getAuditProvinceUsers */,
} from '@/pages/qsmzq/service';
import {
  buildParticipantOptions,
  buildWorkFlowData,
  getNextParticipants,
  getParticipantIds,
} from '@/pages/qsmzq/utils/WoUtils';
import type { FlowLogRecord } from '@/pages/qsmzq/wo/detail/data';
import {
  attachList,
  deleteAttach,
  deleteResourceByNe,
  downloadAttach,
  trsAutoCheckResource,
  trsAutoCheckHandResource
} from '@/pages/qsmzq/wo/detail/service';
import type { UploadFile } from 'antd/es/upload/interface';
import { UploadOutlined } from '@ant-design/icons';
import { getStore } from '@/utils/framework/store';
import moment from 'moment';

interface ComponentProps {
  open: boolean;
  setOpen: (val: boolean) => void;
  onComplete: () => void;
  // 入网或退网
  netType?: TransferNetType;
  woRecord: WorkOrderRecord;
  showAsDescriptions?: boolean;
  // 操作记录日志
  flowLogRecord?: FlowLogRecord;
  workOrderDetail?: WorkOrderDetail;
  // 环节编码
  activityCode: string;
  // 资源实例id
  resInstId: string;
  // 附件组id
  attachGroupId?: string;
  // 省份下拉
  proParticipantOptions?: any[];
  // 非省份下拉
  unproParticipantOptions?: any[];
}

interface ResourceVerifi {
  xh?: number;
  name: string;
  checkContent?: string;
  checkResult?: boolean;
  checkMess?: string;
  checkDate?: string;
  // 退网核对失败列表
  datas?: any[];
}

type Operator = 'pass' | 'reject2dataProduction' | 'reject2draft';

interface SubmitActionForm {
  operator: Operator;
  submitToProv: boolean;
  note?: string;
  remark?: string;
  participantIds: string[];
}

export default ({
  open,
  setOpen,
  onComplete,
  woRecord,
  workOrderDetail,
  showAsDescriptions,
  flowLogRecord,
  netType = 'trsAccess',
  activityCode,
  resInstId,
  attachGroupId = '',
  proParticipantOptions,
  unproParticipantOptions,
}: ComponentProps) => {
  const [modal, contextHolder] = Modal.useModal();
  const [actionForm, setActionForm] = useState<SubmitActionForm>({
    operator: 'pass',
    submitToProv: true,
    participantIds: [],
  });

  const intervalRef = useRef<number | null>(null);

  const tempForm = useRef<SubmitActionForm>({ ...actionForm });

  // 审核人下拉
  const [participantOptions, setParticipantOptions] = useState<any[]>([]);

  const [checkAllResult, setCheckAllResult] = useState<boolean>(false);
  const [tableData, setTableData] = useState<ResourceVerifi[]>([]);

  const [okLoading, setOkLoading] = useState<boolean>();
  const [checkLoading, setCheckLoading] = useState<boolean>();

  // 核对失败详情
  const [failModalVisible, setFailModalVisible] = useState<boolean>(false);
  const [failList, setFailList] = useState<any[]>([]);

  const showFailDetal = (row: ResourceVerifi) => {
    setFailList(row.datas || []);
    setFailModalVisible(true);
  };

  const columns: ProColumns<ResourceVerifi>[] = [
    // {
    //   title: '序号',
    //   dataIndex: 'xh',
    //   width: 80,
    //   align: 'center',
    // },
    // {
    //   title: '资源类型',
    //   dataIndex: 'typeName',
    //   align: 'center',
    // },
    {
      title: '传输网元名称',
      dataIndex: 'name',
      align: 'center',
      onCell: (_, index) => {
        if (((index || 0) & 1) == 0) {
          return {
            rowSpan: 2,
          };
        } /*if (index == 1)*/ else {
          return {
            rowSpan: 0,
          };
        }
        // return {
        //   rowSpan: 1,
        // };
      },
    },
    {
      title: '核对内容',
      dataIndex: 'checkContent',
      align: 'center',
    },
    {
      title: '核对结果',
      dataIndex: 'checkResult',
      render: (_, row) => {
        return row.checkResult ? (
          <div>核对成功</div>
        ) : (
          <div style={{ color: 'red' }}>核对不通过</div>
        );
      },
      align: 'center',
    },
    {
      title: '描述说明',
      dataIndex: 'checkMess',
      align: 'center',
    },
    {
      title: '核对时间',
      dataIndex: 'checkDate',
      align: 'center',
    },
    {
      title: '核对详情',
      dataIndex: 'detail',
      render: (_, row) => {
        return (
          <Space>
            <Button
              hidden={row.checkResult || row.checkMess == '网元不存在于红名单'}
              type={'link'}
              size={'small'}
              onClick={() => {
                showFailDetal(row);
              }}
            >
              核对详情
            </Button>
            {(row.checkResult || row.checkMess == '网元不存在于红名单') && <div>-</div>}
          </Space>
        );
      },
      align: 'center',
    },
  ];

  const updateActionForm = (field: string, val: any) => {
    tempForm.current[field] = val;
    const form: any = { ...tempForm.current };
    setActionForm(form);
  };

  const updateActionFormProperties = (props: any) => {
    const form: any = { ...actionForm };
    Object.assign(form, props || {});
    setActionForm(form);
  };

  const headers = {
    'x-token': getStore('Token'),
  };
  // 附件列表(可初次通过接口获取)
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  // 上传最大文件大小
  const MAX_FILE_SIZE = 20 * 1024 * 1024;
  const accepts: string[] = [
    '.txt',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
    '.pdf',
    '.zip',
    '.rar',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.heic',
  ];
  const handleBeforeUpload = (file: UploadFile, fl: UploadFile[]) => {
    const uploadedCnt = fileList.filter((f) => f.status == 'done').length;
    if (fl.length > 5 - uploadedCnt) {
      message.destroy();
      if (uploadedCnt > 0) {
        if (uploadedCnt >= 5) {
          message.error(
            `最多支持上传5个附件,已上传${uploadedCnt}个附件,请先删除部分附件再上传新的附件`,
          );
        } else {
          message.error(
            `最多支持上传5个附件,已上传${uploadedCnt}个附件,最多选择${5 - uploadedCnt}个`,
          );
        }
      } else {
        message.error('最多支持上传5个附件');
      }
      return Upload.LIST_IGNORE;
    }
    const size: number = file.size as number;
    if (size > MAX_FILE_SIZE) {
      message.error(`附件[${file.name}]大小超过20MB,不能上传`);
      file.status = 'error';
      file.response = `附件[${file.name}]大小超过20MB,不能上传`;
      return Upload.LIST_IGNORE;
    }
    const name = file.name as string;
    const accept = accepts.find((acc) => name.endsWith(acc));
    if (!accept) {
      message.error(`附件[${file.name}]类型不支持`);
      file.status = 'error';
      file.response = `附件[${file.name}]类型不支持`;
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleFileRemove = (file: UploadFile) => {
    if (file.status != 'done') return true;
    return new Promise<void>(async (resolve, reject) => {
      modal.confirm({
        width: 300,
        centered: true,
        title: '',
        content: '确认删除吗',
        onOk: () => {
          deleteAttach(file.uid).then((res) => {
            const { code, msg = '服务端错误' } = res;
            if (code == 200 || code == 2000) {
              message.info(`附件[${file.name}]已删除`);
              resolve();
            } else {
              message.destroy();
              message.error(msg || '服务端错误');
              reject();
            }
          });
        },
        onCancel: () => {
          reject();
        },
      });
    });
  };

  // 加载附件列表
  const loadAttachList = () => {
    const groupKey = attachGroupId as string;
    if (groupKey) {
      attachList(groupKey).then((res) => {
        const { code, data } = res;
        if (code == 200 || code == 2000) {
          setFileList(
            (data || []).map((item: any) => {
              console.log('file item', item);
              const { attId, attOrigName } = item;
              return {
                uid: attId,
                name: attOrigName,
                status: 'done',
              };
            }),
          );
        } else {
          message.destroy();
          message.error('附件列表加载失败');
        }
      });
    } else {
      console.warn('groupKey is null or empty when load attachList');
    }
  };

  const handleUploadChange = (info: any) => {
    console.log('info', info);
    // 当前上传的附件
    const f = info.file as UploadFile;
    if (f.status != 'removed') {
      if (f && f.response) {
        console.log('f.status', f.status);
        const { code, data } = f.response;
        if (code) {
          if (code != 200 && code != 2000) {
            message.error(`附件[${f.name}]上传失败`);
            f.response = `附件[${f.name}]上传失败`;
            f.status = 'error';
          } else {
            message.info(`附件[${f.name}]上传成功`);
            f.uid = data.id;
          }
        } else {
          f.response = `附件[${f.name}]上传失败: 服务端错误`;
          f.status = 'error';
        }
      }
    }
    const newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const handleFileDownload = (file: UploadFile) => {
    console.log('download file ', file);
    const uid = file.uid;
    downloadAttach(uid, file.name);
  };

  // 处理审核人员列表
  // const queryParticipants = (isProvince = false) => {
  //   const promise: Promise<any> = isProvince
  //     ? getAuditProvinceUsers({
  //         processNode: activityCode,
  //         processName: netType,
  //         specialty: netType,
  //         sheetNo: woRecord.sheetNo,
  //         professionalType: '3',
  //       })
  //     : getAuditParticipants({
  //         processNode: activityCode,
  //         processName: netType,
  //         specialty: netType,
  //         sheetNo: woRecord.sheetNo,
  //         professionalType: '3',
  //       });
  //
  //   // 非readonly下加载审核人列表
  //   promise
  //     .then((res) => {
  //       const { code, data } = res;
  //       if (code == 200 || code == 2000) {
  //         const options = (Array.isArray(data) ? data : []).map(
  //           ({ userName, trueName, showName }) => {
  //             return {
  //               key: userName,
  //               value: userName,
  //               label: showName,
  //               trueName,
  //             };
  //           },
  //         );
  //         setParticipantOptions(options);
  //       }
  //     })
  //     .catch(() => {});
  // };

  const setParticipants = (isProvince = false) => {
    const options = isProvince ? proParticipantOptions : unproParticipantOptions;
    console.log('setParticipants', options);
    setParticipantOptions(options as any[]);
  };

  const OperatorDescriptions = (
    <Descriptions
      title={'人工核对'}
      size="small"
      column={1}
      bordered
      labelStyle={{ width: '30%', textAlign: 'center' }}
    >
      <Descriptions.Item
        label={
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>操作选项
          </span>
        }
      >
        <Radio.Group
          disabled={showAsDescriptions}
          value={actionForm.operator}
          onChange={(val) => updateActionForm('operator', val.target.value)}
        >
          <Radio
            // 退网暂时放开
            // disabled={false}
            disabled={!checkAllResult || showAsDescriptions}
            value={'pass'}>
            通过
          </Radio>
          <Radio value={'reject2draft'}>驳回至拟稿</Radio>
        </Radio.Group>
      </Descriptions.Item>
      {actionForm.operator == 'pass' && (
        <>
          <Descriptions.Item
            label={
              <span>
                {/* 是否省分处理 改为 提交到省分 */}
                <span style={{ color: 'red', marginRight: '2px' }}>*</span>提交到省分
              </span>
            }
          >
            <Radio.Group
              disabled={showAsDescriptions}
              value={actionForm.submitToProv}
              onChange={(val) => {
                setParticipants(val.target.value);
                updateActionForm('submitToProv', val.target.value);
                updateActionFormProperties({ participantIds: [], submitToProv: val.target.value });
              }}
            >
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Descriptions.Item>
          {
            <Descriptions.Item
              label={
                <span>
                  <span style={{ color: 'red', marginRight: '2px' }}>*</span>处理人
                </span>
              }
            >
              <Select
                getPopupContainer={(triggerNode: HTMLElement) => triggerNode.parentNode as HTMLElement}
                value={actionForm.participantIds}
                disabled={showAsDescriptions}
                mode={'multiple'}
                showSearch
                optionFilterProp="label"
                placeholder={'请选择处理人'}
                options={participantOptions}
                style={{ width: '100%' }}
                onChange={(val) => updateActionForm('participantIds', val)}
              />
            </Descriptions.Item>
          }
        </>
      )}
      <Descriptions.Item label="审核意见">
        <Input
          disabled={showAsDescriptions}
          placeholder={'请填写审核意见'}
          value={actionForm.note}
          onChange={(evt) => updateActionForm('note', evt.target.value)}
        />
      </Descriptions.Item>
      <Descriptions.Item label="附件">
        <Upload
          disabled={!attachGroupId}
          action={`${API_URL}/qsmzq-common/attach/uploadSingle`}
          accept={accepts.join(',')}
          name={'file'}
          headers={headers}
          fileList={fileList}
          showUploadList={{
            showRemoveIcon: !showAsDescriptions,
            showDownloadIcon: true,
          }}
          multiple
          maxCount={5}
          data={{
            groupKey: attachGroupId,
            sheetCreateTime:
              workOrderDetail?.sheetCreateTime || moment().format('YYYY-MM-DD HH:mm:ss'),
          }}
          beforeUpload={handleBeforeUpload}
          onRemove={handleFileRemove}
          onChange={(info) => handleUploadChange(info)}
          onDownload={(file) => handleFileDownload(file)}
        >
          <Button
            disabled={showAsDescriptions || !attachGroupId}
            type={'primary'}
            icon={<UploadOutlined />}
          >
            点击上传
          </Button>

        </Upload>
        <span style={{ display: 'block', fontSize: '12px' }}>
          （支持上传类型.txt, .doc, .docx, .xls, .xlsx, .ppt, .pdf, .pptx,.pdf, .zip, .rar, .jpg, .jpeg, .png, .gif, .heic最大不超过20M）
        </span>
      </Descriptions.Item>
      <Descriptions.Item label="备注说明">
        <Input
          disabled={showAsDescriptions}
          placeholder={'请填写备注说明'}
          value={actionForm.remark}
          allowClear
          onChange={(evt) => updateActionForm('remark', evt.target.value)}
        />
      </Descriptions.Item>
    </Descriptions>
  );

  /**
   * 后台数据转换为资源核对模型
   *
   * @param data
   */
  const transformResourceVerifi = (
    data: any[],
  ): { resultFlag: boolean; resources: ResourceVerifi[] } => {
    if (data.length == 0) {
      return { resultFlag: false, resources: [] };
    }
    let resultFlag = true;
    const resourceVerifis: ResourceVerifi[] = [];
    let xh = 0;
    for (const item of data) {
      const { neName, neBiz, neRed } = item;
      for (const row of [neBiz, neRed]) {
        const { checkContent, checkDate, checkItems, checkResult, checkMess } = row || {};
        // 核对结果
        const resourceVerifi: ResourceVerifi = {
          xh: ++xh,
          name: neName,
          checkResult: checkResult == '通过',
          checkContent,
          checkMess,
          checkDate,
          datas: checkItems,
        };
        if (!resourceVerifi.checkResult) {
          resultFlag = false;
        }
        resourceVerifis.push(resourceVerifi);
      }
    }
    return { resultFlag, resources: resourceVerifis };
  };

  // 手动点击核对
  const autoCheckHandAndQuery = () => {
    // trsAutoCheckHandResource
    trsAutoCheckHandResource({
      woId: woRecord.woId as string,
    })
      .then((res) => {
        const { code, data, status, msg } = res;
        if (code == 200 || code == 2000) {
          autoCheckAndQuery();
        } else {

        }
      })
      .catch((err) => {
      });
  }

  // 自动核对
  const autoCheckAndQuery = (resultId?: string) => {
    setCheckLoading(true);
    setTableData([]);
    // if (typeof resultId == 'object') {
    //   // eslint-disable-next-line no-param-reassign
    //   resultId = '';
    // }
    trsAutoCheckResource({
      woId: woRecord.woId as string,
      resultId: resultId,
    })
      .then((res) => {

        const { code, data, status } = res;
        if (code == 200 || code == 2000) {
          if (data.result && intervalRef.current == null && data.result == 'RUN') {
            message.info(data['message']);
            const interval = setInterval(() => {
              autoCheckAndQuery(data.batchId);
            }, 1000 * 10);
            intervalRef.current = interval as any;
          }

          if(data.result == 'RUN'){
            return;
          }

          if (!data.result && data.batchId) {
            clearInterval(intervalRef.current as number);
            intervalRef.current = null;
            if (data['checkData'] && data['checkData'].length > 0) {
              message.info('核对成功');
            } else {
              message.info(data['message']);
            }
            console.log("ResourceVerificationModal.tsx ==============================================>")
            const { resultFlag, resources } = transformResourceVerifi(data?.checkData ?? []);
            setTableData(resources);
            setCheckAllResult(resultFlag);
            if (resultFlag) {
              // 核对成功后调用删除资源接口
              deleteResourceByNe(woRecord.woId as string);
              if (actionForm.operator == 'reject2draft') {
                // 核对通过，并且操作是驳回数据制作/驳回拟稿时重置
                updateActionForm('operator', 'pass');
              }
            } else {
              if (actionForm.operator == 'pass') {
                // 核对不通过，并且操作是通过时重置
                updateActionForm('operator', 'reject2draft');
              }
            }
            setCheckLoading(false);
          } else {
            if (!data.batchId) {
              message.info(data['message']);
              setCheckLoading(false);
              setTableData([]);
              clearInterval(intervalRef.current as number);
              intervalRef.current = null;
            } else if (data.result) {
              message.open({
                type: 'info',
                content: data['message'],
                duration: 16,
              });
              setCheckLoading(false);
              setTableData([]);
              clearInterval(intervalRef.current as number);
              intervalRef.current = null;
            }
            // else if (data.result && data.result == -1) {
            //   message.open({
            //     type: 'info',
            //     content: data['message'],
            //     duration: 16,
            //   });
            //   setCheckLoading(false);
            //   setTableData([]);
            // }

          }
        } else {
          setCheckLoading(false);
          message.destroy();
          message.error('核对失败' + (status == 500 ? '：服务端错误' : data['message']));
          if (actionForm.operator == 'pass') {
            // 核对不通过，并且操作是通过时重置
            updateActionForm('operator', 'reject2draft');
          }
          // 测试数据
          setTableData([]);
          clearInterval(intervalRef.current as number);
          intervalRef.current = null;
        }
      })
      .catch((err) => {
        setCheckLoading(false);
        setCheckAllResult(false);
        // message.error('核对失败');
        console.error(err);
        clearInterval(intervalRef.current as number);
        intervalRef.current = null;
      });
    // .finally(() => {
    //   setCheckLoading(false);
    // });
  };

  // 更新
  useEffect(() => {
    // 工单详情 - 操作表单回显
    if (flowLogRecord) {
      const form = Object.assign(
        { operator: 'pass', submitToProv: true, note: '' },
        {
          operator: flowLogRecord.actionName,
          note: flowLogRecord.processSuggestion,
          remark: flowLogRecord.remark || flowLogRecord.opRequest?.remark,
          submitToProv: !!flowLogRecord.opRequest.isProvincialApproval,
          participantIds: getParticipantIds(flowLogRecord.opRequest?.workFlow?.nextParticipant),
        },
      );
      // 更新操作表单
      setActionForm(form as SubmitActionForm);
      // 下拉虚拟options
      setParticipantOptions(
        buildParticipantOptions(flowLogRecord.opRequest?.workFlow?.nextParticipant),
      );
      //加载附件
      loadAttachList();
    }
    // 查询核对资源列表
    if (open) {
      if (woRecord) {
        autoCheckAndQuery('');
      }
      // 统一首次查询省份
      updateActionForm('submitToProv', true);
      // 加载人员列表
      setParticipants(true);
    }

    return () => { };
  }, [flowLogRecord, woRecord, open]);

  useEffect(() => {
    //组件卸载时清除定时器
    return () => {
      clearInterval(intervalRef.current as number);
      intervalRef.current = null;
    };
  }, []);

  if (showAsDescriptions) {
    return OperatorDescriptions;
  }

  const submit = () => {
    if (checkLoading) {
      message.info('核对操作正在执行，请等待核对完成再提交');
      return;
    }
    // validate something
    const flowData = buildWorkFlowData(woRecord);
    flowData.note = actionForm.note;
    flowData.remark = actionForm.remark;
    flowData.operator = actionForm.operator;
    if (actionForm.operator == 'pass') {
      if (!checkAllResult) {
        message.warn('当前资源列表核对未通过');
        return;
      }
      // 校验是否核对通过
      flowData.isProvincialApproval = actionForm.submitToProv;
      // 无论是否提交到省份都需要选择受理人 2024-10-12 修改
      if (true || flowData.isProvincialApproval) {
        const nextParticipants = getNextParticipants(actionForm.participantIds, participantOptions);
        // 设置下一步提交人
        if (nextParticipants.length == 0) {
          message.warn('请选择处理人');
          return;
        }
        flowData.workFlow.nextParticipant = nextParticipants;
      }
    }

    setOkLoading(true);
    completeTask(netType as string, flowData)
      .then((res) => {
        const { code, msg = '服务端错误' } = res;
        if (code == 200 || code == 2000) {
          message.info('操作成功');
          setOpen(false);
          onComplete();
          // 返回
        } else {
          message.destroy();
          message.error('操作失败: ' + msg);
        }
      })
      .catch((err) => {
        message.error('操作失败: ' + err);
      })
      .finally(() => {
        setOkLoading(false);
      });
  };
  const openFalse = () => {
    setOpen(false);
    clearInterval(intervalRef.current as number);
    intervalRef.current = null;
  };
  return (
    <Space>
      <Modal
        title={'资源核对'}
        width={'75%'}
        open={open}
        onOk={() => submit()}
        onCancel={() => openFalse()}
        okButtonProps={{
          loading: okLoading,
          // disabled: actionForm.operator == 'pass' && !checkResult,
        }}
        okText={'提交'}
      >
        <Space direction="vertical" size="small" style={{ display: 'flex' }}>
          <Card
            title={netType == 'trsAccess' ? '入网资源核对记录' : '退网资源核对记录'}
            extra={
              <Button type={'primary'} loading={checkLoading} onClick={autoCheckHandAndQuery}>
                核对
              </Button>
            }
          >
            <ProTable
              rowKey="xh"
              search={false}
              columns={columns}
              tableAlertRender={false}
              toolBarRender={false}
              expandable={{ defaultExpandAllRows: false }}
              dataSource={tableData}
              loading={checkLoading}
              pagination={false}
              bordered
              scroll={{ y: 'calc(100vh - 500px)' }}
            />
          </Card>
          {OperatorDescriptions}
        </Space>

        {/** 资源失败详情 */}
        <Modal
          title="失败详情列表"
          width={'75%'}
          open={failModalVisible}
          onCancel={() => setFailModalVisible(false)}
          okButtonProps={{ hidden: true }}
          cancelButtonProps={{ hidden: true }}
          closable={true}
        >
          <ProTable
            rowKey="checkMessage"
            defaultSize={'small'}
            toolBarRender={false}
            tableAlertRender={false}
            search={false}
            columns={[
              {
                title: '资源类型',
                dataIndex: 'resType',
                align: 'center',
                width: '200px',
              },
              {
                title: '资源名称',
                dataIndex: 'name',
                align: 'center',
              },
              {
                title: '资源ID',
                dataIndex: 'gid',
                align: 'center',
                width: '200px',
              },
              // {
              //   title: '描述说明',
              //   dataIndex: 'checkMessage',
              //   align: 'center',
              // },
            ]}
            dataSource={failList}
            pagination={false}
          />
        </Modal>
      </Modal>
      {contextHolder}
    </Space>
  );
};
