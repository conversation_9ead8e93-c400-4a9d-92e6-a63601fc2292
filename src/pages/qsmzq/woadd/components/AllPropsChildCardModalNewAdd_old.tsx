// eslint-disable-next-line @typescript-eslint/no-unused-vars
// 板卡列表维护
import type { ChildCard } from '@/pages/qsmzq/data';
import { useEffect, useRef, useState } from 'react';
import { Button, Col, Divider, Form, Input, InputNumber, Modal, Row, Select, Tabs, message } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import CardTypeSelect from '@/pages/qsmzq/woadd/components/CardTypeSelect';
import DatePickerAsString from '@/pages/qsmzq/components/DatePickerAsString';
import CardModelSelect from '@/pages/qsmzq/woadd/components/CardModelSelect';
import {
  saveBCard
} from '@/pages/qsmzq/woadd/service';

interface ComponentProps {
  childCard: ChildCard | null;
  readonly?: boolean;
  open: boolean;
  setOpen: (val: boolean) => void;
  enumMap: any;
  newFormData: any;
  woId: any;
  superResName: string,
  editRowProps: string
}

export default ({ open, setOpen, readonly = false, enumMap, childCard, woId, newFormData, superResName, editRowProps }: ComponentProps) => {
  console.log('card of editRowProps', editRowProps);
  // form ref

  const [form] = Form.useForm();
  // console.log("const [form] = Form.useForm(); ====================> ", form);
  const modalTitle = useRef<string>('板卡全属性');
  const [collapse, setCollapse] = useState<boolean>(true);

  const getPopupContainer = (triggerNode: HTMLElement) => triggerNode.parentNode as HTMLElement;

  /**
   * 添加/编辑
   */
  const handleLocalSave = () => {
    const row = form.getFieldsValue(true);
    const trsRmeCardAccessList = [row];
    // console.log('row============================》', trsRmeCardAccessList);
    // console.log('row============================》', woId);
    console.log("row============================》", row);

    form.validateFields().then(() => {
      // 这里要把所有数据传递回去

      if (childCard) {
        Object.assign(childCard, row);
      }
      console.log("childCard============================》", childCard);

      if(editRowProps == 'edit'){
        
      }
      // 校验通过关闭弹出框，取消请求
      setOpen(false);
    });

    // form.validateFields().then(() => {
    // 这里要把所有数据传递回去
    //   saveBCard(woId, trsRmeCardAccessList).then((res) => {
    //     const { code, msg } = res;
    //     if (code == 200 || code == 2000) {
    //       // 刷新列表
    //     } else {
    //       message.destroy();
    //       message.error(`保存失败: ${msg}`);
    //     }
    //   })
    //     .catch((err) => {
    //       message.error('保存失败');
    //     })
    //     .finally(() => {
    //       // setSaveNeLoading(false);
    //     });
    //   if (childCard) {
    //     Object.assign(childCard, row);
    //   }
    //   // 校验通过关闭弹出框，取消请求
    //   setOpen(false);
    // });
  };

  // watch device
  useEffect(() => {
    if (open) {
      form.resetFields();
      form.setFieldsValue(childCard || {});
      setCollapse(true);
    }
    form.setFieldValue('trsNeName', superResName[0]);
    form.setFieldValue('neTypeDesc', superResName[1]);
  }, [open]);

  useEffect(() => {
    console.log("我是formData我变化了！！！", newFormData);
  }, [newFormData]);

  useEffect(() => {
    // superResName
    console.log("superResName,superResName,superResName,superResName,superResName,superResName,superResName,superResName,superResName", form.getFieldValue('name'))
    // form.setFieldValue('name', superResName);
    // form.setFieldsValue()
  }, [superResName])


  // 必填属性
  const getRequiredFormItems = () => {
    // 必填属性编辑模式
    if (!readonly) {
      return [
        <Col key={'required-1'} span={24}>
          {/* <Divider
            type={'horizontal'}
            orientation={'left'}
            dashed
            style={{ fontWeight: 550, fontSize: '.9em' }}
          >
            预交维校验字段
          </Divider> */}
          <Row>
            {[
              <Col span={8} key={'cardName'}>
                <Form.Item
                  label={'板卡名称'}
                  name={'cardName'}
                  style={{ marginBottom: '12px' }}
                  rules={[{ required: true, validateTrigger: 'blur', message: '请输入板卡名称' }]}
                >
                  <Input
                    placeholder={'请输入板卡名称'}
                    allowClear
                    onBlur={(event) => {
                      const name = event.target.value;
                      if (event.target.value) {
                        const cardNo = form.getFieldValue('cardNo');
                        const slotNo = form.getFieldValue('slotNo');
                        if (!cardNo || !slotNo) {
                          try {
                            const reg = /.*\((.*?)\)\/(.*?)\(.*?\).*/g;
                            const match = reg.exec(name);
                            console.log('match', match, match && match[1], match && match[2]);
                            if (match && match[1] && match[2]) {
                              if (!cardNo) {
                                form.setFieldValue('cardNo', match[1] + ':NA-' + match[2]);
                              }
                              if (!slotNo) {
                                const index = match[2].indexOf('-');
                                if (index > -1) {
                                  form.setFieldValue('slotNo', match[2].substring(index + 1));
                                }
                              }
                            }
                          } catch (e) { }
                        }
                      }
                    }}
                  />
                </Form.Item>
              </Col>,
              <Col span={8} key={'cardNo'}>
                <Form.Item
                  label={'板卡编号'}
                  name={'cardNo'}
                  style={{ marginBottom: '12px' }}
                  rules={[{ required: true, validateTrigger: 'blur', message: '请输入板卡编号' }]}
                >
                  <Input placeholder={'请输入板卡编号'} />
                </Form.Item>
              </Col>,
              <Col span={8} key={'cardModelId'}>
                <Form.Item
                  label={'板卡型号'}
                  name={'cardModelId'}
                  style={{ marginBottom: '12px' }}
                  rules={[{ required: true, validateTrigger: 'change', message: '请选择板卡型号' }]}
                >
                  <Input placeholder={'请输入板卡型号'} />
                  {/* <CardModelSelect
                    form={form}
                    readonly={readonly}
                    columnId={'cardModelId'}
                    columnName={'cardModel'}
                  /> */}
                </Form.Item>
              </Col>,
              <Col span={8} key={'trsNeName'}>
                <Form.Item
                  label={'网元名称'}
                  name={'trsNeName'}
                  style={{ marginBottom: '12px' }}
                  required
                >
                  <Input disabled placeholder={'请输入网元名称'} />
                </Form.Item>
              </Col>,
              // 字段待定
              <Col span={8} key={'neTypeDesc'}>
                <Form.Item name={'neTypeDesc'} label={'网元类型'} style={{ marginBottom: '12px' }} required>
                  <Input disabled placeholder={'请输入网元类型'} />
                </Form.Item>
              </Col>,
              <Col span={8} key={'slotNo'}>
                <Form.Item
                  label={'槽位号'}
                  name={'slotNo'}
                  style={{ marginBottom: '12px' }}
                  rules={[{ required: true, message: '请输入槽位号' }]}

                >
                  <InputNumber min={1} style={{ width: '100%' }} placeholder={'请输入槽位号'} />
                </Form.Item>
              </Col>,

            ]}
          </Row>
        </Col>,
        // <Col key={'required-2'} span={24}>
        //   {/* <Divider
        //     type={'horizontal'}
        //     orientation={'left'}
        //     dashed
        //     style={{ fontWeight: 550, fontSize: '.9em' }}
        //   >
        //     预交维非校验字段
        //   </Divider> */}
        //   {/* <Row>
        //     {[


        //       <Col span={8} key={'cardInstallDate'}>
        //         <Form.Item label={'入网时间'} name={'installDate'} style={{ marginBottom: '12px' }}>
        //           <DatePickerAsString
        //             showTime
        //             placeholder={'请选择入网时间'}
        //             allowClear
        //             style={{ width: '100%' }}
        //           />
        //         </Form.Item>
        //       </Col>,
        //       <Col span={8} key={'superResId'}>
        //         <Form.Item
        //           label={'所属设备标识'}
        //           name={'superResId'}
        //           style={{ marginBottom: '12px' }}
        //         >
        //           <Input placeholder={'请输入所属设备标识'} />
        //         </Form.Item>
        //       </Col>,
        //     ]}
        //   </Row> */}
        // </Col>,
      ];
    }

    // 必填属性只读模式
    // return [
    //   <Col span={8} key={'cardName'}>
    //     <Form.Item
    //       label={'板卡名称'}
    //       name={'cardName'}
    //       style={{ marginBottom: '12px' }}
    //       rules={[{ required: true, validateTrigger: 'blur', message: '请输入板卡名称' }]}
    //     >
    //       <Input placeholder={'请输入板卡名称'} allowClear />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'cardNo'}>
    //     <Form.Item
    //       label={'板卡编号'}
    //       name={'cardNo'}
    //       style={{ marginBottom: '12px' }}
    //       rules={[{ required: true, validateTrigger: 'blur', message: '请输入板卡编号' }]}
    //     >
    //       <Input placeholder={'请输入板卡编号'} />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'cardModelId'}>
    //     <Form.Item
    //       label={'板卡型号'}
    //       name={'cardModel'}
    //       style={{ marginBottom: '12px' }}
    //       rules={[{ required: true, validateTrigger: 'change', message: '请选择板卡型号' }]}
    //     >
    //       <CardModelSelect
    //         form={form}
    //         readonly={readonly}
    //         columnId={'cardModelId'}
    //         columnName={'cardModel'}
    //       />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'mntStateId'}>
    //     <Form.Item label={'维护状态'} name={'mntStateId'} style={{ marginBottom: '12px' }} required>
    //       <Select
    //         placeholder={'请选择维护状态'}
    //         allowClear
    //         showSearch
    //         optionFilterProp="label"
    //         getPopupContainer={getPopupContainer}
    //         options={(enumMap.mntState || []).map((item: any) => ({
    //           label: item.dictName,
    //           value: item.dictCode,
    //           key: item.dictCode,
    //         }))}
    //       />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'oprStateId'}>
    //     <Form.Item label={'业务状态'} name={'oprStateId'} style={{ marginBottom: '12px' }} required>
    //       <Select
    //         placeholder={'请选择业务状态'}
    //         allowClear
    //         showSearch
    //         optionFilterProp="label"
    //         getPopupContainer={getPopupContainer}
    //         options={(enumMap.oprState || []).map((item: any) => ({
    //           label: item.dictName,
    //           value: item.dictCode,
    //           key: item.dictCode,
    //         }))}
    //       />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'cardInstallDate'}>
    //     <Form.Item
    //       label={'入网时间'}
    //       name={'installDate'}
    //       style={{ marginBottom: '12px' }}
    //       required
    //     >
    //       <DatePickerAsString
    //         showTime
    //         placeholder={'请选择入网时间'}
    //         allowClear
    //         style={{ width: '100%' }}
    //       />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'superResTypeName'}>
    //     <Form.Item label={'所属设备类型'} style={{ marginBottom: '12px' }} required>
    //       <Input disabled value={'OLT'} placeholder={'请输入所属设备类型'} />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'superResId'}>
    //     <Form.Item
    //       label={'所属设备标识'}
    //       name={'superResId'}
    //       style={{ marginBottom: '12px' }}
    //       rules={[{ required: true, validateTrigger: 'blur', message: '请输入所属设备标识' }]}
    //     >
    //       <Input disabled placeholder={'请输入所属设备标识'} />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'superResName'}>
    //     <Form.Item
    //       label={'所属设备名称'}
    //       name={'superResName'}
    //       style={{ marginBottom: '12px' }}
    //       required
    //     >
    //       <Input disabled placeholder={'请输入所属设备名称'} />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'type'}>
    //     <Form.Item label={'资源类型'} style={{ marginBottom: '12px' }} required>
    //       <Input disabled value={'板卡'} />
    //     </Form.Item>
    //   </Col>,
    //   <Col span={8} key={'slotNo'}>
    //     <Form.Item label={'槽位号'} name={'slotNo'} style={{ marginBottom: '12px' }}>
    //       <Input placeholder={'请输入槽位号'} />
    //     </Form.Item>
    //   </Col>,
    // ];
  };

  // 选填属性
  const getOptionalFormItems = () => {
    return [
      <Col span={8} key={'nmsOrigResName'}>
        <Form.Item
          label={'板卡采集前置名称'}
          name={'nmsOrigResName'}
          style={{ marginBottom: '12px' }}
        >
          <Input placeholder={'请输入板卡采集前置名称'} />
        </Form.Item>
      </Col>,
      <Col span={8} key={'nmsOrigResId'}>
        <Form.Item
          label={'板卡采集前置标识'}
          name={'nmsOrigResId'}
          style={{ marginBottom: '12px' }}
        >
          <Input placeholder={'请输入板卡采集前置标识'} />
        </Form.Item>
      </Col>,
      <Col span={8} key={'emsOrigResName'}>
        <Form.Item label={'板卡网管名称'} name={'emsOrigResName'} style={{ marginBottom: '12px' }}>
          <Input placeholder={'请输入板卡网管名称'} />
        </Form.Item>
      </Col>,
      <Col span={8} key={'emsOrigResId'}>
        <Form.Item label={'板卡网管标识'} name={'emsOrigResId'} style={{ marginBottom: '12px' }}>
          <Input placeholder={'请输入板卡网管标识'} />
        </Form.Item>
      </Col>,
      <Col span={8} key={'cardTypeId'}>
        <Form.Item label={'板卡类型'} name={'cardTypeId'} style={{ marginBottom: '12px' }}>
          <CardTypeSelect
            form={form}
            readonly={readonly}
            columnId={'cardTypeId'}
            columnName={'cardType'}
          />
        </Form.Item>
      </Col>,
      <Col span={8} key={'cardSerialNo'}>
        <Form.Item label={'板卡序列号'} name={'cardSerialNo'} style={{ marginBottom: '12px' }}>
          <Input placeholder={'请输入板卡序列号'} allowClear />
        </Form.Item>
      </Col>,
      // 数据还没有拿到
      <Col span={8} key={'specialityId'}>
        <Form.Item label={'专业类型'} name={'specialityId'} style={{ marginBottom: '12px' }}>
          <Select
            placeholder={'请选择专业类型'}
            allowClear
            showSearch
            optionFilterProp="label"
            getPopupContainer={getPopupContainer}
            options={(enumMap.speciality || []).map((item: any) => ({
              label: item.dictName,
              value: item.dictCode,
              key: item.dictCode,
            }))}
          />
        </Form.Item>
      </Col>,
      <Col span={8} key={'mntStateId'}>
        <Form.Item label={'维护状态'} name={'mntStateId'} style={{ marginBottom: '12px' }}>
          <Select
            placeholder={'请选择维护状态'}
            allowClear
            showSearch
            optionFilterProp="label"
            getPopupContainer={getPopupContainer}
            options={(enumMap.mntState || []).map((item: any) => ({
              label: item.dictName,
              value: item.dictCode,
              key: item.dictCode,
            }))}
          />
        </Form.Item>
      </Col>,
      // 没数据
      <Col span={8} key={'oprStateId'}>
        <Form.Item label={'业务状态'} name={'oprStateId'} style={{ marginBottom: '12px' }}>
          <Select
            placeholder={'请选择业务状态'}
            allowClear
            showSearch
            optionFilterProp="label"
            getPopupContainer={getPopupContainer}
            options={(enumMap.oprState || []).map((item: any) => ({
              label: item.dictName,
              value: item.dictCode,
              key: item.dictCode,
            }))}
          />
        </Form.Item>
      </Col>,
      <Col span={8} key={'installDate'}>
        <Form.Item label={'入网时间'} name={'installDate'} style={{ marginBottom: '12px' }}>
          <DatePickerAsString
            showTime
            placeholder={'请选择入网时间'}
            allowClear
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Col>,
      // 没数据
      // 不明确字段
      <Col span={8} key={'belongNetwork'}>
        <Form.Item label={'所属网络'} name={'belongNetwork'} style={{ marginBottom: '12px' }}>
          <Select
            disabled={false}
            placeholder={'请选择所属网络'}
            allowClear
            showSearch
            optionFilterProp="label"
            getPopupContainer={getPopupContainer}
            options={(enumMap.belongNetwork || []).map((item: any) => ({
              label: item.dictName,
              value: item.dictCode,
              key: item.dictCode,
            }))}
          />
        </Form.Item>
      </Col>,
      // 不明确字段
      <Col span={8} key={'type'}>
        <Form.Item label={'资源类型'} style={{ marginBottom: '12px' }}>
          <Input disabled value={'板卡'} />
        </Form.Item>
      </Col>,
      // 不明确字段
      <Col span={8} key={'rackNo'}>
        <Form.Item label={'机柜号'} name={'rackNo'} style={{ marginBottom: '12px' }}>
          <Input placeholder={'请输入机柜号'} />
        </Form.Item>
      </Col>,
      // 不明确字段
      <Col span={8} key={'shelfNo'}>
        <Form.Item label={'机框号'} name={'shelfNo'} style={{ marginBottom: '12px' }}>
          <Input placeholder={'请输入机框号'} />
        </Form.Item>
      </Col>,
      // 不明确字段
      <Col span={8} key={'plmn'}>
        <Form.Item label={'承建方PLMN'} name={'plmn'} style={{ marginBottom: '12px' }}>
          <Input placeholder={'请输入承建方PLMN'} />
        </Form.Item>
      </Col>,
















      // <Col span={8} key={'alias'}>
      //   <Form.Item label={'别名'} name={'alias'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入别名'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'hdweVer'}>
      //   <Form.Item label={'硬件版本号'} name={'hdweVer'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入硬件版本号'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'swVer'}>
      //   <Form.Item label={'软件版本号'} name={'swVer'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入软件版本号'} allowClear />
      //   </Form.Item>
      // </Col>,



      // <Col span={8} key={'lifeCycle'}>
      //   <Form.Item label={'资源生命周期'} name={'lifeCycle'} style={{ marginBottom: '12px' }}>
      //     <Select
      //       disabled
      //       placeholder={'请选择资源生命周期'}
      //       allowClear
      //       showSearch
      //       optionFilterProp="label"
      //       getPopupContainer={getPopupContainer}
      //       options={(enumMap.lifeCycle || []).map((item: any) => ({
      //         label: item.dictName,
      //         value: item.dictCode,
      //         key: item.dictCode,
      //       }))}
      //     />
      //   </Form.Item>
      // </Col>,




      // <Col span={8} key={'gnodebId'}>
      //   <Form.Item label={'所属GNODEB ID'} name={'gnodebId'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入所属GNODEB ID'} />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'pSyncId'}>
      //   <Form.Item label={'同步标识ID'} name={'pSyncId'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入同步标识ID'} />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'datacollectTime'}>
      //   <Form.Item label={'数据采集时间'} name={'datacollectTime'} style={{ marginBottom: '12px' }}>
      //     <DatePickerAsString
      //       showTime
      //       placeholder={'请选择数据采集时间'}
      //       allowClear
      //       style={{ width: '100%' }}
      //     />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'notes'}>
      //   <Form.Item label={'备注'} name={'notes'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入备注'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'createOp'}>
      //   <Form.Item label={'创建人'} name={'createOp'} style={{ marginBottom: '12px' }}>
      //     <Input disabled placeholder={'创建人'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'createDate'}>
      //   <Form.Item label={'创建时间'} name={'createDate'} style={{ marginBottom: '12px' }}>
      //     <Input disabled placeholder={'创建时间'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'modifyOp'}>
      //   <Form.Item label={'修改人'} name={'modifyOp'} style={{ marginBottom: '12px' }}>
      //     <Input disabled placeholder={'修改人'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'modifyDate'}>
      //   <Form.Item label={'修改时间'} name={'modifyDate'} style={{ marginBottom: '12px' }}>
      //     <Input disabled placeholder={'修改时间'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'resLockedState'}>
      //   <Form.Item label={'资源封锁状态'} name={'resLockedState'} style={{ marginBottom: '12px' }}>
      //     <Select
      //       disabled
      //       placeholder={'请选择资源封锁状态'}
      //       allowClear
      //       getPopupContainer={getPopupContainer}
      //       options={[
      //         { label: '是', value: '1', key: 1 },
      //         { label: '否', value: '0', key: 0 },
      //       ]}
      //     />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'pConfirmStatus'}>
      //   <Form.Item label={'确认状态'} name={'pConfirmStatus'} style={{ marginBottom: '12px' }}>
      //     <Select
      //       disabled
      //       placeholder={'请选择确认状态'}
      //       allowClear
      //       showSearch
      //       optionFilterProp="label"
      //       getPopupContainer={getPopupContainer}
      //       options={(enumMap.pConfirmStatus || []).map((item: any) => ({
      //         label: item.dictName,
      //         value: item.dictCode,
      //         key: item.dictCode,
      //       }))}
      //     />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'collectCardModelId'}>
      //   <Form.Item
      //     label={'采集板卡型号'}
      //     name={'collectCardModelId'}
      //     style={{ marginBottom: '12px' }}
      //   >
      //     <CardModelSelect
      //       form={form}
      //       readonly={readonly}
      //       columnId={'collectCardModelId'}
      //       columnName={'collectCardModel'}
      //     />
      //   </Form.Item>
      // </Col>,

      // <Col span={8} key={'dataSource'}>
      //   <Form.Item label={'数据来源'} name={'dataSource'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入数据来源'} allowClear />
      //   </Form.Item>
      // </Col>,
      // <Col span={8} key={'workOrderNo'}>
      //   <Form.Item label={'入网工单'} name={'workOrderNo'} style={{ marginBottom: '12px' }}>
      //     <Input placeholder={'请输入入网工单'} allowClear />
      //   </Form.Item>
      // </Col>,
    ];
  };

  return (
    <>
      <Modal
        title={modalTitle.current}
        width={'78%'}
        open={open}
        onOk={handleLocalSave}
        onCancel={() => {
          setOpen(false);
        }}
        okButtonProps={{ hidden: readonly }}
        cancelButtonProps={{ hidden: readonly }}
        closable={true}
      >
        <Form form={form} disabled={readonly} labelCol={{ span: 7, offset: 1 }}>
          <Tabs
            type="card"
            activeKey="required-attributes"
            style={{ margin: '10px 0' }}
            items={[
              {
                key: 'required-attributes',
                label: '必填属性',
                children: <Row>{getRequiredFormItems()}</Row>,
              },
            ]}
          />
          <Tabs
            type="card"
            activeKey="optional-attributes"
            style={{ margin: '10px 0' }}
            items={[
              {
                key: 'optional-attributes',
                label: '选填属性',
                children: <Row
                // hidden={collapse}
                >{getOptionalFormItems()}</Row>,
              },
            ]}
          // tabBarExtraContent={
          //   <Button
          //     type={'link'}
          //     disabled={false}
          //     icon={collapse ? <DownOutlined /> : <UpOutlined />}
          //     onClick={() => {
          //       setCollapse(!collapse);
          //     }}
          //   >
          //     {collapse ? '展开全部' : '收起全部'}
          //   </Button>
          // }
          />
        </Form>
      </Modal>
    </>
  );
};
