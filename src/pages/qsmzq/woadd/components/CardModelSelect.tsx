/**
 * 板卡型号选择组件
 */
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { FormInstance } from 'antd';
import { Button, Col, Form, Input, Modal, Row, Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { queryCardModel } from '@/pages/qsmzq/woadd/service';
import MfrSelect from '@/pages/qsmzq/woadd/components/MfrSelect';
import CardTypeSelect from '@/pages/qsmzq/woadd/components/CardTypeSelect';

interface Props {
  form: FormInstance;
  readonly?: boolean;
  columnId: string;
  columnName: string;
  rowData?: any;
  onDataChange?: any;
  confirmVisible?: any;
  newCardModel?: any;
}

export default ({ form, readonly = false, columnId, columnName, rowData, onDataChange, confirmVisible, newCardModel }: Props) => {
  console.log('render cardmode ... ', columnId, columnName);
  console.log('value, newCardModel-newCardModel ... ', newCardModel);
  const columnText = form.getFieldValue(columnName);
  // 安置地点类型为空时禁用
  const disabled = readonly;
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  // form ref
  const [queryForm] = Form.useForm();


  // useEffect(()=>{
  //   if(confirmVisible){
  //     setModalVisible(true);
  //   }
  // }, [confirmVisible])
  // 表格选择器
  const [selectedRow, setSelectedRow] = useState<object | null>(null);
  const onSelectChange = (newSelectedRowKeys: React.Key[], d: any[]) => {
    if (d.length > 0) {
      setSelectedRow(d[0]);
    }
  };

  const rowSelection = {
    onChange: onSelectChange,
  };

  const columns: ProColumns<any>[] = [
    {
      title: '板卡类型ID',
      dataIndex: 'cardModelId',
      align: 'center',
    },
    {
      title: '板卡型号',
      dataIndex: 'cardModel',
      align: 'center',
    },
    {
      title: '厂家名称',
      dataIndex: 'mfrName',
      align: 'center',
    },
    {
      title: '板卡类型',
      dataIndex: 'cardType',
      align: 'center',
    },
    {
      title: '端口数',
      dataIndex: 'portNum',
      align: 'center',
    },
  ];

  const searchData = () => {
    if (actionRef.current) {
      (actionRef.current as any).reloadAndRest();
    }
  };

  const clearValue = () => {
    // if (onChange) {
    //   console.log("onChange([...value, form.getFieldValue('columnName')]);", onChange);
    //   // onChange([...value, form.getFieldValue('columnName')]);
    // }
    form.setFieldValue(columnId, null);
    form.setFieldValue(columnName, null);

  };


  // useEffect(()=>{
  //   form.setFieldValue(columnName, newCardModel);
  // }, newCardModel)
  const handleOk = () => {
    console.log('当前选择的数据： ', selectedRow);
    if (onDataChange) {
      onDataChange(selectedRow)
    }
    const obj: any = selectedRow as any;
    const value = obj.cardModelId;
    form.setFieldValue(columnName, obj.cardModel);
    // 设置id的值控制校验
    form.setFields([
      { name: columnId, value, errors: [] },
      { name: columnName, errors: [] },
    ]);
    // 专门给板卡编码的
    if (rowData) {
      rowData[columnName] = obj.cardModel;
    }
    setModalVisible(false);
  };

  const resetData = () => {
    queryForm.resetFields();
    // searchData();
  };

  // 初始化
  useEffect(() => {
    if (modalVisible) {
      resetData();
    }
  }, [modalVisible]);

  return (
    <>
      <Input.Search
        disabled={disabled}
        value={columnText}
        placeholder={'请选择板卡型号'}
        onSearch={(val, e) => {
          if (e?.target['ariaLabel'] == 'search') {
            setModalVisible(true);
          }
        }}
        allowClear
        onChange={() => clearValue()}
      />

      <Modal
        title={'请选择板卡型号'}
        open={modalVisible}
        width={'75%'}
        onOk={handleOk}
        onCancel={() => setModalVisible(false)}
        okButtonProps={{ disabled: !selectedRow }}
        closable={true}
        zIndex={2001}
      >
        <Space direction="vertical" size="small" style={{ display: 'flex' }}>
          <Form form={queryForm} labelCol={{ span: 6, offset: 1 }}>
            <Row>
              <Col span={12} key={'cardModel'}>
                <Form.Item label="板卡型号" name="cardModel" style={{ marginBottom: '12px' }}>
                  <Input placeholder={'请输入设备型号'} allowClear />
                </Form.Item>
              </Col>
              <Col span={12} key={'cardTypeId'}>
                <Form.Item label="板卡类型" name="cardTypeId" style={{ marginBottom: '12px' }}>
                  <CardTypeSelect
                    form={queryForm}
                    columnId={'cardTypeId'}
                    columnName={'cardType'}
                  />
                </Form.Item>
              </Col>
              <Col span={12} key={'mfrId'}>
                <Form.Item label="厂家名称" name="mfrId" style={{ marginBottom: '12px' }}>
                  <MfrSelect form={queryForm} columnId={'mfrId'} columnName={'mfrName'} />
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <ProTable
            rowKey={'cardModelId'}
            actionRef={actionRef}
            columns={columns}
            tableAlertRender={false}
            rowSelection={{
              type: 'radio',
              ...rowSelection,
            }}
            search={false}
            defaultSize={'small'}
            pagination={{
              defaultPageSize: 10,
            }}
            toolbar={{
              search: (
                <Space>
                  <Button type={'primary'} onClick={searchData}>
                    查询
                  </Button>
                  <Button
                    onClick={() => {
                      queryForm.resetFields();
                      searchData();
                    }}
                  >
                    重置
                  </Button>
                </Space>
              ),
            }}
            manualRequest={true}
            request={async (params) => {
              const { current: pageNum, pageSize } = params;
              const formParams = queryForm.getFieldsValue();
              const query = { ...formParams, pageNum, pageSize };
              // 板卡型号假数据
              // return {
              //   data: [{
              //     "mfrId": "90000686",
              //     "portNum": null,
              //     "cardType": "",
              //     "mfr": "北电",
              //     "pageSize": 0,
              //     "cardModelId": "1",
              //     "cardTypeId": "0",
              //     "pageNum": 0,
              //     "cardModel": "FELC"
              //   },
              //   {
              //     "mfrId": "90001600",
              //     "portNum": null,
              //     "cardType": "",
              //     "mfr": "天津光电",
              //     "pageSize": 0,
              //     "cardModelId": "2",
              //     "cardTypeId": "0",
              //     "pageNum": 0,
              //     "cardModel": "FE128"
              //   },
              //   {
              //     "mfrId": "90000686",
              //     "portNum": null,
              //     "cardType": "",
              //     "mfr": "北电",
              //     "pageSize": 0,
              //     "cardModelId": "3",
              //     "cardTypeId": "0",
              //     "pageNum": 0,
              //     "cardModel": "ALC"
              //   }], total: 3, success: true
              // }
              const { code, data } = await queryCardModel(query);


              if (code == 200 || code == 2000) {
                const { records, total } = data;
                return { data: records, total, success: true };
              } else {
                return { data: [], total: 0, success: false };
              }
            }}
          />
        </Space>
      </Modal>
    </>
  );
};
