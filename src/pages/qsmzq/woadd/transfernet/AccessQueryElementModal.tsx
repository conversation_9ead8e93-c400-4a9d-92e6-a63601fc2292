import { message, Modal, Space } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import React, { useEffect, useRef, useState } from 'react';
import type { NetworkElement } from '@/pages/qsmzq/data';
import { queryNetworkElements, saveNeList } from '../service';
// import PositIdSelect from '@/pages/qsmzq/woadd/components/PositIdSelect';
import styles from '../index.less';

// 扩展NetworkElement类型，添加索引签名以支持动态属性访问
interface ExtendedNetworkElement extends NetworkElement {
  [key: string]: any;
}

interface ComponentProps {
  open: boolean;
  setOpen: (val: boolean) => void;
  enumMap: Record<string, any>;
  woId: string;
  maxSelectSize: number;
  reload: () => void;
  selectedRowIds: any[];
  selectType: 'radio' | 'checkbox' | undefined;
  sendMessage?: (data: any[]) => void;
}

// 注意：这些全局变量和未使用的参数可能在将来的功能中使用
// 如果确定不需要，可以在后续重构中移除
// let alradaySelectGid: React.Key[] = [];

export default ({ open, setOpen, enumMap, woId, maxSelectSize: _, reload, selectedRowIds: __, selectType, sendMessage }: ComponentProps) => {
  // 使用下划线表示未使用的参数
  const actionRef = useRef<ActionType>();
  const tableData = useRef<ExtendedNetworkElement[]>([]);

  // 表格选择器
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRowName, setSelectedRowName] = useState<string | undefined>(undefined);
  const [selectedRow, setSelectedRow] = useState<ExtendedNetworkElement | null>(null);

  const onSelectChange = (newSelectedRowKeys: React.Key[], rows: ExtendedNetworkElement[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    if (rows && rows.length > 0) {
      // 确保name是字符串类型
      const name = rows[0]?.name || '';
      setSelectedRowName(typeof name === 'string' ? name : String(name));
      setSelectedRow(rows[0]);
    }
  };

  const [okLoading, setOkLoading] = useState<boolean>(false);

  // 定义正确的rowSelection类型
  const rowSelection = {
    type: selectType,
    selectedRowKeys,
    onChange: onSelectChange as any, // 使用类型断言解决类型不匹配问题
  };
  const hasSelected = selectedRowKeys.length > 0;
  const columns: ProColumns<NetworkElement>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      width: 150,
      search: false,
      render: (_, _row, index) => { // 使用下划线前缀表示未使用的参数
        let pageOffset = 0;
        if (actionRef?.current?.pageInfo) {
          pageOffset =
            (actionRef.current.pageInfo.current - 1) * actionRef.current.pageInfo.pageSize;
        }
        return <div style={{ width: '80px' }}>{pageOffset + index + 1}</div>;
      },
    },
    {
      title: '网元名称',
      dataIndex: 'name',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '网元ID',
      dataIndex: 'gid',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    // {
    //   title: '网元类型',
    //   dataIndex: 'neTypeDesc',
    //   align: 'center',
    //   search: false,
    //   width: 150,
    //   ellipsis: true,
    // },
    // {
    //   title: 'IP地址',
    //   dataIndex: 'ipAddress',
    //   align: 'center',
    //   width: 150,
    //   search: false,
    //   ellipsis: true,
    // },
    {
      title: '传输网元厂家',
      dataIndex: 'mantainDept',
      // key: 'mantainDept',
      align: 'center',
      width: 150,
      search: false,
      ellipsis: true,
    },
    // <Form.Item
    //   label={'资源生命周期'}
    //   name={'lifeCycle'}
    //   style={{ marginBottom: '12px' }}
    // >
    //   <Select
    //     // disabled={true}
    //     placeholder={'请选择资源生命周期'}
    //     allowClear
    //     showSearch
    //     optionFilterProp="label"
    //     getPopupContainer={getPopupContainer}
    //     options={(enumMap.lifeCycle || []).map((item: any) => ({
    //       label: item.dictName,
    //       value: item.dictCode,
    //       key: item.dictCode,
    //     }))}
    //   />
    // </Form.Item>
    // {
    //   search: false,
    //   title: '全生命周期状态',
    //   dataIndex: 'lifeCycle',
    //   valueType: 'select',
    //   align: 'center',
    //   width: 150,
    //   ellipsis: true,
    //   fieldProps: {
    //     options: (enumMap.lifeCycle || []).map((enumOption: any) => ({
    //       label: enumOption.dictName,
    //       value: enumOption.dictCode,
    //       key: enumOption.dictCode,
    //     })),
    //   },
    // },
    {
      title: '安置地点类型',
      dataIndex: 'positTypeDesc',
      valueType: 'select',
      search: false,
      width: 150,
      ellipsis: true,
      fieldProps: {
        options: (enumMap.positType || []).map((enumOption: any) => ({
          label: enumOption.dictName,
          value: enumOption.dictCode,
          key: enumOption.dictCode,
        })),
      },
      align: 'center',
    },
    {
      title: '所属安置地点',
      dataIndex: 'roomName',
      search: false,
      // render: (_, row) => <div>{row.roomName}</div>,
      // renderFormItem: (_, { type, defaultRender, ...rest }, form) => (
      //   <PositIdSelect
      //     form={form}
      //     columnId={'positId'}
      //     columnName={'positName'}
      //     positTypeId={positTypeId}
      //   />
      // ),
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '网络级别',
      dataIndex: 'networklayer',
      align: 'center',
      search: false,
      width: 150,
      ellipsis: true,
    },
    {
      title: '网络层次',
      dataIndex: 'sysLevel',
      search: false,
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    // mntManName
    {
      title: '包机人',
      dataIndex: 'mntManName',
      search: false,
      align: 'center',
      width: 150,
      ellipsis: true,
    },
  ];

  // useEffect(() => {
  //   // console.log("selectedRowIds useEffect()================================================>", selectedRowIds);
  //   selectedRowIds.forEach(item => {
  //     alradaySelectGid.splice(alradaySelectGid.indexOf(item), 1);
  //   })
  // }, [selectedRowIds])

  const handleOk = () => {
    console.log(selectedRowKeys, selectedRowName, selectedRow);

    if (selectType === 'radio' && selectedRow) {
      console.log("不保存了");
      setOpen(false);
      // 打印selectedRow对象，查看可用字段
      console.log('selectedRow完整对象:', selectedRow);

      // 打印selectedRow对象的所有属性，帮助调试
      console.log('selectedRow所有属性:', Object.keys(selectedRow));

      // 安全地访问selectedRow属性
      const tmpData = [
        selectedRowKeys[0],
        selectedRowName,
        selectedRow.neTypeDesc || '',
        selectedRow.positTypeDesc || '',
        selectedRow.roomName || selectedRow.positName || '',
        selectedRow.ipAddress || selectedRow.manageIpaddress || '',
        selectedRow.mantainDept || '', // 使用正确的字段名
        selectedRow.regionName || '',
        selectedRow.mntManName || '',
        selectedRow.positTypeId || '',
        selectedRow.mntMan || '', // 添加包机人ID字段
      ];

      // 传递回父组件（如果sendMessage存在）
      if (sendMessage) {
        sendMessage(tmpData);
      }
      return;
    }



    // const resultSelect = selectedRowKeys.filter(item => {
    //   console.log(alradaySelectGid.includes(item));
    //   if (!alradaySelectGid.includes(item)) {
    //     alradaySelectGid.push(item);
    //     return item;
    //   }
    //   return false;
    // })


    // selectedRowKeys = resultSelect;
    // console.log("resultSelect=============================>", resultSelect);
    // setSelectedRowKeys(resultSelect);
    // console.log("selectedRowKeys ==============================> ", selectedRowKeys);



    // let tip = 0;
    // if (resultSelect.length === 0) {
    //   tip = 1;
    // } else if (resultSelect.length < selectedRowKeys.length) {
    //   tip = 2;
    // }
    const rows = tableData.current.filter((row) => selectedRowKeys.includes(row.gid || ''));
    // console.log('选择rows', rows);
    setOkLoading(true);
    saveNeList(woId, rows)
      .then((res) => {
        const { code, data } = res; // 移除未使用的msg变量
        if ((code == 200 || code == 2000) && data["changeTotal"] != 0) {
          // 关闭弹窗
          message.success(`保存成功`)
          setOpen(false);
          // 刷新列表
          reload();
        } else {
          message.destroy();
          message.warning(`${data["message"]}`);
        }
      })
      .catch((_) => { // 使用下划线表示未使用的参数
        message.error('保存失败');
      })
      .finally(() => {
        setOkLoading(false);
      });
  };

  useEffect(() => {
    if (open) {
      setSelectedRowKeys([]);
    }
  }, [open]);

  return (
    <Modal
      title={'网元查询'}
      width={'80%'}
      open={open}
      onOk={() => handleOk()}
      okButtonProps={{
        loading: okLoading,
        disabled: !hasSelected,
      }}
      onCancel={() => setOpen(false)}
    >
      <Space direction="vertical" size="small" style={{ display: 'flex', margin: '-20px 0' }}>
        <ProTable
          className={styles.transfernetTable}
          actionRef={actionRef}
          rowKey="gid"
          rowSelection={rowSelection}
          pagination={{
            defaultPageSize: 10,
          }}
          defaultSize={'small'}
          tableAlertRender={false}
          search={{
            span: 8,
            defaultCollapsed: false,
          }}
          form={{
            labelWidth: 120,
            initialValues: {
              positTypeId: '2080001',
            },
          }}
          columns={columns}
          scroll={{ x: 'auto' }}
          manualRequest={true}
          request={async (params) => {
            console.log('params', params);
            const { current: pageNum, pageSize, ...otherProps } = params;
            const query = { pageNum, pageSize, ...otherProps };
            tableData.current = [];
            const { code, data } = await queryNetworkElements(query);
            if (code == 200 || code == 2000) {
              const { records: list, total } = data;
              tableData.current = list;
              return { data: list, total, success: true };
            } else {
              return { data: [], total: 0, success: false };
            }
          }}
        />
      </Space>
    </Modal>
  );
};
