import type { FormInstance } from 'antd';
import {
  Badge,
  Button,
  Card,
  Col,
  Empty,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Tabs,
} from 'antd';
import styles from '../index.less';
import { DeleteOutlined, DownloadOutlined, ImportOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { type ResizableColumnsType, useAntdResizableHeader } from 'use-antd-resizable-header'
import ProTable from '@ant-design/pro-table';
import type { MutableRefObject } from 'react';
import React, { useEffect, useRef, useState, useMemo } from 'react';
import type { NetworkElement } from '@/pages/qsmzq/data';
import WithdrawalQueryElementModal from '@/pages/qsmzq/woadd/transfernet/WithdrawalQueryElementModal';
import {
  downloadTrsWithdrawalTemplate,
  deleteNetworkElement,
  enumListGroup,
  queryNetworkElementByWoId,
  queryTrsResourceCount,
  queryTrsResourceTable,
  importTrsWithdrawalNeFile,
  exportNeList,
} from '@/pages/qsmzq/woadd/service';

import type { TabColumnData, TabGroupData, TabGroupModel } from '../data.d';
import ChildResourceTable from './ChildResourceTable';
import ChildPortResourceTree from './ChildPortResourceTree';
import type { WorkOrderDetail } from '@/pages/qsmzq/data';
import Styles from '../index.less';
import staticEnum from './api/staticEnum.json';
/**
 * 提供属性支持其他环节复用
 */
interface ExitProps {
  // 是否只读状态
  readonly?: boolean;
  // 工单id
  woId?: string | null;
  // 工单号
  sheetNo?: string | null;
  // 工单记录信息
  detailOrderRef?: MutableRefObject<WorkOrderDetail>;
  // 是否禁止提交
  setSubmitDisabled?: (disabled: boolean) => void;
  // 检查工单是否可用
  checkSheetDisabled?: (callback: () => void) => void;
  // form实例
  attachForm: FormInstance;
  // 工单名称
  sheetTitle?: string | null;
}

export default ({
  readonly = false,
  woId,
  sheetNo,
  detailOrderRef,
  setSubmitDisabled = (disabled: boolean) => { },
  checkSheetDisabled = (callback: () => void) => { },
  attachForm,
  sheetTitle,
}: ExitProps) => {
  const getPopupContainer = (triggerNode: HTMLElement) => triggerNode.parentNode as HTMLElement;
  const [refKey, setRefKey] = useState<number>(1);
  const [tabGroupModels, setTabGroupModels] = useState<TabGroupModel[]>([]);
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  const [tabValueCountMap, setTabValueCountMap] = useState<object>({});

  const [selectNeRow, setSelectNeRow] = useState<NetworkElement | null>(null);
  const actionRef = useRef<ActionType>();
  const [tableData, setTableData] = useState<NetworkElement[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // 枚举字段列表
  const [enumMap, setEnumMap] = useState<any>({});
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  // 详情信息
  const [detailForm] = Form.useForm();
  const [detailModalTitle, setDetailModalTitle] = useState<string>('');
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  // const [collapse, setCollapse] = useState<boolean>(true);
  const [requiredItems, setRequiredItems] = useState<any[]>([]);
  const [optionalItems, setOptionalItems] = useState<any[]>([]);

  // 导入
  const fileInput = useRef<HTMLInputElement>(null);
  const [importLoading, setImportLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const enumKeys = [
    'positType',
    'aBandWidth',
    'aRate',
    'bandRate',
    'bandWidth',
    'circuitType',
    'direction',
    'neCap',
    'neModel',
    'netWork',
    'netWorkDesc',
    'networklayer',
    'neType',
    'oprStateId',
    'protectMode',
    'rate',
    'routeType',
    'segType',
    'sysLevel',
    'useage',
    'lifeCycle',
    'mntState',
    'circuitLevel',
    'circuitRate',
    'circuitGrade',
    'routeType',
    'isbak',
    'resourceSpecification',
    // 'loadLevel',
    'purpose',
    'isMsp',
    'pointTypeId',
    'isunion',
    'portTypeId',
    'oprState',
    'linkType',
  ];
  const columnEnumKeyMap = {
    positTypeId: 'positType',
    mntStateId: 'mntState',
    // 起始设施类型
    beginPointTypeId: 'pointTypeId',
    // 终止设施类型
    endPointTypeId: 'pointTypeId',
    loadLevel: 'circuitLevel',
    //资源类型
    resTypeId: 'resourceSpecification',
  };

  enumKeys.forEach((enumKey) => {
    columnEnumKeyMap[enumKey] = enumKey;
  });

  const queryEnumMap = () => {
    enumListGroup(enumKeys).then((res) => {
      const { code, data } = res;
      if (code == 200 || code == 2000) {
        for (const k in columnEnumKeyMap) {
          const value = columnEnumKeyMap[k];
          if (data[value]) {
            data[k] = data[value];
          }
        }
        // 构造静态枚举列表
        for (const staticK of staticEnum.data) {
          data[staticK.key] = staticK.enumData;
        }

        setEnumMap(data);
      }
    });
  };

  // 将表格字段转为表单字段列表支持必填和选填
  const tableColumnsToFormItems = (tableColumns: ProColumns<any>[], required: boolean) => {
    let uniqueIndex = 0;
    tableColumns = tableColumns.filter((item) => item.dataIndex !== 'deleteStatus');
    return tableColumns
      .filter((column) => {
        if (!column.dataIndex) return false;
        if (required) {
          return !column.formItemProps || !!(column.formItemProps as any).required == true;
        } else {
          return column.formItemProps && !!(column.formItemProps as any).required == false;
        }
      })
      .map((column) => {
        const columnKey = column.dataIndex as string;
        let component = <Input /*placeholder={'请输入' + column.title} */ />;
        const enumKey = columnEnumKeyMap[columnKey];
        if (enumKey && enumMap[enumKey]) {
          component = (
            <Select
              // placeholder={`请选择${column.title}`}
              allowClear
              showSearch
              optionFilterProp="label"
              getPopupContainer={getPopupContainer}
              options={(enumMap[enumKey] || []).map((item: any) => ({
                label: item.dictName,
                value: item.dictCode,
                key: item.dictCode,
              }))}
            />
          );
        }

        const colKey = column.dataIndex + '-' + ++uniqueIndex;
        return (
          <Col span={8} key={colKey}>
            <Form.Item
              label={column.title as string}
              name={column.dataIndex}
              required={false}
              style={{ marginBottom: '12px' }}
            >
              {component}
            </Form.Item>
          </Col>
        );
      });
  };

  const columns: ProColumns<NetworkElement>[] = [
    {
      title: '删除状态',
      key: 'deleteStatus',
      dataIndex: 'deleteStatus',
      align: 'center',
      hideInForm: true,
      width: 90,
      render: (_, row) => {
        if (row.deleteStatus == '1') {
          return '已删除';
        } else {
          return '未删除';
        }
      },
    },
    {
      title: '网元ID',
      dataIndex: 'gid',
      key: 'gid',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '网元编号',
      dataIndex: 'no',
      key: 'no',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '设备别名',
      dataIndex: 'alias',
      key: 'alias',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '网元名称',
      key: 'name',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
      width: 250,
    },
    {
      title: '网元采集前置名称',
      key: 'nmsOrigResName',
      dataIndex: 'nmsOrigResName',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '设备型号',
      dataIndex: 'eqpMode',
      key: 'eqpMode',
      width: 90,
      ellipsis: true,
      align: 'center',
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 90,
      ellipsis: true,
      align: 'center',
    },
    {
      title: '传输网元厂家',
      dataIndex: 'mantainDept',
      key: 'mantainDept',
      width: 160,
      ellipsis: true,
      align: 'center',
    },
    {
      title: '所属管理区域',
      dataIndex: 'regionName',
      key: 'regionName',
      width: 150,
      ellipsis: true,
      align: 'center',
    },
    {
      title: '传输网元类型',
      dataIndex: 'neTypeDesc',
      key: 'neTypeDesc',
      width: 150,
      ellipsis: true,
      align: 'center',
    },
    {
      title: '安置地点类型',
      dataIndex: 'positTypeDesc',
      key: 'positTypeDesc',
      width: 120,
      align: 'center',
    },
    {
      title: '所属安置地点',
      dataIndex: 'roomName',
      key: 'roomName',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '网络级别',
      dataIndex: 'networklayer',
      key: 'networklayer',
      width: 90,
      align: 'center',
    },
    {
      title: '网络层次',
      dataIndex: 'sysLevel',
      key: 'sysLevel',
      width: 90,
      align: 'center',
    },
    {
      title: '传输网元采集前置标识',
      dataIndex: 'nmsOrigResId',
      key: 'nmsOrigResId',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '传输网元采集前置名称',
      dataIndex: 'nmsOrigResName',
      key: 'nmsOrigResName',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '传输网元采集前置名称',
      dataIndex: 'nmsOrigResName',
      key: 'nmsOrigResName',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '包机人',
      dataIndex: 'mntPerson',
      key: 'mntPerson',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '软件版本',
      dataIndex: 'swVer',
      key: 'swVer',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '产权归属',
      dataIndex: 'propertyAffiliatedDesc',
      key: 'propertyAffiliatedDesc',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '产权性质',
      dataIndex: 'propertySourceDesc',
      key: 'propertySourceDesc',
      align: 'center',
      hideInTable: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      hideInTable: true,
    },

    {
      title: '修改时间',
      dataIndex: 'modiryDate',
      key: 'modiryDate',
      align: 'center',
      hideInTable: true,
    },
    // {
    //   title: 'IP地址',
    //   dataIndex: 'ipAddress',
    //   key: 'ipAddress',
    //   align: 'center',
    //   hideInTable: true,
    // },
    {
      title: '控制器ID',
      dataIndex: 'controllerId',
      key: 'controllerId',
      align: 'center',
      hideInTable: true,
    },

    {
      title: '操作',
      align: 'center',
      key: 'ops',
      width: 100,
      fixed: 'right',
      render: (_, row) => (
        <Button
          size={'small'}
          type={'link'}
          onClick={() => {
            setRequiredItems(tableColumnsToFormItems(columns, true));
            setOptionalItems(tableColumnsToFormItems(columns, false));
            detailForm.resetFields();
            detailForm.setFieldsValue(row);
            setDetailModalTitle('网元详情');
            setDetailModalVisible(true);
          }}
        >
          网元详情
        </Button>
      ),
    },
  ];

  const [times, setTimes] = useState(0)
  const { components, resizableColumns, tableWidth } = useAntdResizableHeader({
    columns: useMemo(() => columns, [times]),
  })


  const queryTabResourceGroups = () => {
    const toTableColumns = (tabColumnDatas: TabColumnData[]) => {
      // console.log("const tableColumns = tabColumnDatas.map((tabColumnData) =>", tabColumnDatas)
      const tableColumns = tabColumnDatas.map((tabColumnData) => {

        const disableSearch = tabColumnData.hideInTable || !tabColumnData.supportedSearch;
        const col: ProColumns<any> = {
          title: tabColumnData.label,
          key: tabColumnData.key,
          dataIndex: tabColumnData.key,
          hideInTable: !!tabColumnData.hideInTable,
          align: 'center',
          width: 200,
          ellipsis: true,
          formItemProps: {
            required: tabColumnData.required != false && tabColumnData.required != 'false',
          },
          ...(disableSearch ? { search: false } : {}),
        };
        return col;
      });
      return tableColumns;
    };

    // 添加woId
    queryTrsResourceTable(woId as string).then((res) => {
      console.log(' queryTrsResourceTable ', res.data.records);
      const { code, data } = res;
      if (code == 200 || code == 2000) {
        // tabGroupData -> TabGroupModel
        // const table = res.data.records;
        // console.log(table);

        const groupModels: TabGroupModel[] = (data as TabGroupData[]).map((tabGroupData) => {
          const { tableName, tableKey, isShow, tabColumns, ...otherProps } = tabGroupData;
          return {
            tableName: tableName,
            tableKey: tableKey,
            isShow,
            ...otherProps,
            tabColumns: toTableColumns(tabColumns),
            // actionRef: useRef<ActionType>(),
          };
        });
        setTabGroupModels(groupModels);
        setActiveTabKey(groupModels[0].tableKey);
      }
    });
  };

  const getChildGroupModel = (tabGroupModel: TabGroupModel): TabGroupModel => {
    return tabGroupModels.find(
      (groupModel: TabGroupModel) => groupModel.tableKey == tabGroupModel.tabChildKey,
    ) as TabGroupModel;
  };

  // 表格选择器
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const hasSelected = selectedRowKeys.length > 0;

  const isFirstReload = useRef(true);
  const isFirstReloadNew = useRef(true);
  /**
   * 刷新表格数据
   */
  const reloadTableData = () => {
    setTableLoading(true);
    queryNetworkElementByWoId(woId as string)
      .then((res) => {
        const { code, data } = res;
        console.log('============ ', res);
        if (code == 200 || code == 2000) {
          // const { eqpList } = data || {};
          if (data) {
            console.log("const rows: NetworkElement[] = Array.isArray(data) ? data : [data];", data);
            if (!data['records']) {
              data['records'] = []
            }
            const rows: NetworkElement[] = Array.isArray(data) ? data : [data];

            setTableData(rows);
            if (rows.length > 0) {
              setSelectNeRow(rows[0]);
            }
            setSubmitDisabled(false);
            if (rows.length <= 0) {
              setSelectNeRow(null);
            }
          } else {
            setTableData([]);
            setSubmitDisabled(true);
          }

        } else {
          console.error(res);
        }

        if (isFirstReloadNew.current) {
          isFirstReloadNew.current = false;
          setTimeout(() => {
            console.log(actionRef)
            reloadTableData();
          }, 1666);
        }

      })
      .catch((err) => {
        message.error('查询失败');
      })
      .finally(() => {
        setTableLoading(false);
        // 解决提交置灰 
        if (isFirstReload.current) {
          isFirstReload.current = false;
          console.log(isFirstReload);
          reloadTableData();
        }
      });


  };



  const loadTrsResourceCount = () => {
    queryTrsResourceCount(woId as string, selectNeRow?.gid as string)
      .then((res) => {
        const { code, data } = res;
        console.log('============ ', res);
        if (code == 200 || code == 2000) {
          const countMap = {};
          for (const item of data || []) {
            const { resName, cnt = 0 } = item;
            if (resName) {
              countMap[resName] = cnt;
            }
          }
          setTabValueCountMap(countMap);
        } else {
          console.error(res);
        }
      })
      .catch((err) => {
        message.error('查询失败');
      })
      .finally(() => { });
  };
  // 关联查询
  const loadAllTrsResourceData = () => {
    setRefKey(refKey + 1);
    loadTrsResourceCount();
  };
  /**
   * 删除网元
   */
  const handleDeleteSelected = () => {
    if (hasSelected) {
      setDeleteLoading(true);
      // 删除的id集合(理论上只有一个)
      deleteNetworkElement(woId as string, selectedRowKeys)
        .then((res) => {
          const { code, msg = '服务端错误' } = res;
          if (code == 200 || code == 2000) {
            message.info('删除成功');
            reloadTableData();
            setSelectedRowKeys([]);
            if (tableData.length <= 0) {
              setSelectNeRow(null);
            }

            //loadAllTrsResourceData();
          } else {
            message.destroy();
            message.error('删除失败: ' + msg);
          }
        })
        .catch((err) => {
          message.error('删除失败');
        })
        .finally(() => {
          setDeleteLoading(false);
        });
    }
  };

  const loadAllData = () => {
    reloadTableData();
    // loadTrsResourceCount();
    if (tabGroupModels.length > 0) {
      setActiveTabKey(tabGroupModels[0].tableKey);
    }
  };



  // upload
  const onFileInputChange = (evt: any) => {
    const files = evt.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      const formData = new FormData();
      formData.append('file', file);
      setImportLoading(true);
      // upload formData
      importTrsWithdrawalNeFile(woId, sheetNo, formData)
        .then((res) => {
          console.log(res);
          const { code, data, msg } = res;
          if (code == 2000 || code == 200) {
            console.log(data);
            if (data == null || data.length == 0) {
              // message.error({
              //   content: (
              //     <div style={{ textAlign: 'left' }}>
              //       <div>
              //         <b>导入失败:{msg} </b>
              //       </div>
              //       {/*{msgList.map((msg) => (*/}
              //       {/*  <div>{msg}</div>*/}
              //       {/*))}*/}
              //     </div>
              //   ),
              // });
              message.error('导入失败：' + msg);
            } else {
              message.success('导入成功');
              loadAllData();
              // sheetNo: 0, sheetName: "OLT"
              // const msgList = [];
              // for (const item of data) {
              //   const { sheetNo: index, sheetName, dataInfo } = item;
              //   let msg = `第${index + 1}个sheet页[${sheetName}]: `;
              //   msg += dataInfo.join(' ');
              //   msgList.push(msg);
              // }
            }
          } else {
            message.destroy();
            message.error('导入失败：' + msg);
          }
        })
        .catch((err) => {
          message.error('导入失败：' + err.msg);
        })
        .finally(() => {
          setImportLoading(false);
        });
    }
    // clear file area values
    evt.target.value = null;
  };

  // 当前选择网元变化时触发关联查询
  useEffect(() => {
    if (selectNeRow) {
      loadAllTrsResourceData();
    }
  }, [selectNeRow]);

  /** woId变化时 */
  useEffect(() => {
    setSelectNeRow(null);
    if (!woId) {
      setTableData([]);
    } else {
      console.log('watch woId change ', woId);
      loadAllData();
    }
  }, [woId]);

  /**
   * 加载枚举列表
   */
  useEffect(() => {
    queryEnumMap();
    queryTabResourceGroups();
  }, []);
  useEffect(() => {
    if (sheetTitle) {
      attachForm.setFieldsValue({
        sheetTitle: sheetTitle,
      });
    }
  }, [sheetTitle]);

  /**
   * 显示详情
   *
   * @param tabGroupModel
   * @param row
   */
  const showDetail = (tabGroupModel: TabGroupModel, row: any) => {
    // setCollapse(false);
    detailForm.resetFields();
    detailForm.setFieldsValue(row);
    setRequiredItems(tableColumnsToFormItems(tabGroupModel.tabColumns, true));
    setOptionalItems(tableColumnsToFormItems(tabGroupModel.tabColumns, false));
    setDetailModalTitle(`${tabGroupModel.tableName}详情`);
    setDetailModalVisible(true);
  };
  const handleExport = () => {
    const params = {
      woId: woId,
      eqpIds: selectedRowKeys,
    };
    setExportLoading(true);
    exportNeList(params, () => {
      setExportLoading(false);
    });
  };
  return (
    <>
      <Card className={styles.transfernetCard} title="业务信息" size={'small'}>
        <Form
          form={attachForm}
          labelCol={{ style: { width: '120px' }, offset: 0 }}
          disabled={readonly}
          style={{ margin: '10px 0' }}
        >
          <Row>
            <Col span={8}>
              <Form.Item
                labelCol={{ style: { width: '120px' } }}
                label={'联系人'}
                name={'per'}
                style={{ marginBottom: '10px' }}
              >
                <div>{detailOrderRef?.current.senderName}</div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                labelCol={{ style: { width: '120px' } }}
                label={'申请单位'}
                name={'unit'}
                style={{ marginBottom: '10px' }}
              >
                <div>{detailOrderRef?.current.senderDeptName}</div>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                labelCol={{ style: { width: '120px' } }}
                label={'退网资源类型'}
                name={'powerConsumption'}
                style={{ marginBottom: '10px' }}
              >
                <div>传输网元</div>
              </Form.Item>
            </Col>
            {readonly == false ? (
              <>
                <Col span={8}>
                  <Form.Item
                    labelCol={{ style: { width: '120px' } }}
                    label={'工单编号'}
                    name={'sheetNo'}
                    style={{ marginBottom: '10px' }}
                  >
                    <div>{sheetNo}</div>
                  </Form.Item>
                </Col>
                <Col span={16}>
                  <Form.Item
                    labelCol={{ style: { width: '120px' } }}
                    wrapperCol={{ span: 24 }}
                    label={'工单主题'}
                    name={'sheetTitle'}
                    style={{ marginBottom: '10px' }}
                    // onChange={(e: any) => attachForm.setFieldValue('sheetTitleNew', e.target.value)}
                    rules={[
                      {
                        validator: (rule, value, callback) => {
                          if (!value) {
                            callback('请输入工单主题');
                          }
                        },
                        required: true,
                      },
                    ]}
                  >
                    <Input placeholder={'工单主题内容格式:原主题内容-XXXXXX'} allowClear />
                  </Form.Item>
                </Col>
              </>
            ) : (
              ''
            )}
            <Col span={24}>
              <Form.Item
                labelCol={{ style: { width: '120px' } }}
                wrapperCol={{ span: 24 }}
                label={'备注说明'}
                name={'reasonForApplication'}
                style={{ marginBottom: '10px' }}
              >
                <Input.TextArea placeholder={'请输入备注说明'} allowClear />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Space hidden={readonly}>
          <Button
            type="primary"
            disabled={tableData.length >= 20 || !woId}
            icon={<PlusOutlined />}
            onClick={() => {
              checkSheetDisabled(() => {
                setModalVisible(true);
              });
            }}
            title={'最多能添加20个资源'}
          >
            选择资源
          </Button>
          <Popconfirm
            title="确定要删除吗?"
            okText="确定"
            cancelText="取消"
            disabled={!hasSelected}
            onConfirm={handleDeleteSelected}
          >
            <Button
              type="primary"
              loading={deleteLoading}
              danger
              disabled={!hasSelected}
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>

          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => {
              downloadTrsWithdrawalTemplate(() => { });
            }}
          >
            下载模板
          </Button>
          <Button
            disabled={!woId || tableData.length >= 20}
            loading={importLoading}
            type="primary"
            icon={<ImportOutlined />}
            onClick={() => {
              checkSheetDisabled(() => {
                fileInput.current?.click();
              });
            }}
          >
            EXCEL模板导入
          </Button>
          <input
            ref={fileInput}
            style={{ display: 'none' }}
            type="file"
            name="file"
            accept=".xlsx,.xls"
            onChange={onFileInputChange}
          />
        </Space>
        <ProTable
          // style={{
          //   overflow: 'hidden',
          //   overflowX: "scroll"
          // }}
          actionRef={actionRef}
          rowKey="gid"
          className={'trsWithdrawalTable'}
          defaultSize={'small'}
          rowSelection={readonly ? false : rowSelection}
          options={{
            reload: () => {
              // reloadTableData();
              loadAllData();
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                if (record != selectNeRow) {
                  setSelectNeRow(record);
                  // loadAllTrsResourceData();
                }
              },
            };
          }}
          toolBarRender={() => [
            <Button key="exportList" loading={exportLoading} type="primary" onClick={handleExport}>
              导出
            </Button>,
          ]}
          rowClassName={(record) => {
            return record == selectNeRow ? 'ne-row-selected' : '';
          }}
          tableAlertRender={false}
          search={false}
          // columns={columns}
          columns={resizableColumns}
          components={components}
          scroll={{ x: tableWidth }}
          dataSource={tableData}
          onLoad={reloadTableData}
          loading={tableLoading}
          pagination={false}
        />
      </Card>

      {/* 关联资源 */}
      <Card
        key={refKey}
        hidden={!selectNeRow}
        className={styles.transfernetCard}
        title={
          <div>
            <span style={{ fontWeight: 'bold' }}>{selectNeRow?.name}</span>
            <span>的关联资源</span>
          </div>
        }
        size={'small'}
      >
        {!selectNeRow && <Empty />}
        {selectNeRow && (
          <Tabs
            type="card"
            activeKey={activeTabKey}
            onChange={setActiveTabKey}
            items={[
              ...tabGroupModels
                .filter((tabGroupModel) => tabGroupModel.isShow)
                .map((tabGroupModel) => ({
                  key: tabGroupModel.tableKey,
                  label: (
                    <Badge
                      showZero
                      offset={[0, -6]}
                      count={tabValueCountMap[tabGroupModel.tableKey] || 0}
                      overflowCount={100000}
                      size={'small'}
                    >
                      <span style={{ marginRight: '10px' }}>{tabGroupModel.tableName}</span>
                    </Badge>
                  ),
                  children: [
                    <ChildResourceTable
                      woId={woId as string}
                      key={tabGroupModel.tableKey}
                      onShowDetail={showDetail}
                      enumMap={enumMap}
                      getChildGroupModel={getChildGroupModel}
                      tabGroupModel={tabGroupModel}
                      selectNeRow={selectNeRow as NetworkElement}
                    />,
                  ],
                })),
              {
                key: 'childInfo',
                label: '子级信息',
                children: (
                  <ChildPortResourceTree
                    woId={woId as string}
                    onShowDetail={showDetail}
                    enumMap={enumMap}
                    selectNeRow={selectNeRow as NetworkElement}
                  />
                ),
              },
            ]}
          />
        )}
      </Card>

      {/* 资源网元模态框 */}
      <WithdrawalQueryElementModal
        open={modalVisible}
        setOpen={setModalVisible}
        enumMap={enumMap}
        woId={woId as string}
        maxSelectSize={10 - tableData.length}
        reload={loadAllData}
      />

      {/* 全属性 - 详情信息 */}
      <Modal
        title={detailModalTitle}
        width={'85%'}
        open={detailModalVisible}
        onOk={() => { }}
        onCancel={() => setDetailModalVisible(false)}
        okButtonProps={{ hidden: true }}
        cancelButtonProps={{ hidden: true }}
        closable={true}
      >
        <Form
          className={Styles.trsDetailForm}
          form={detailForm}
          disabled={true}
          labelCol={{ span: 10, offset: 1 }}
        >
          <Row key={'1'}>
            {requiredItems}
            {optionalItems}
          </Row>
          {/*<Tabs*/}
          {/*  type="card"*/}
          {/*  activeKey="required-attributes"*/}
          {/*  style={{ margin: '10px 0' }}*/}
          {/*  items={[*/}
          {/*    {*/}
          {/*      key: 'required-attributes',*/}
          {/*      label: '必填属性',*/}
          {/*      children: <Row key={'1'}>{requiredItems}</Row>,*/}
          {/*    },*/}
          {/*  ]}*/}
          {/*/>*/}
          {/*<Tabs*/}
          {/*  hidden={optionalItems.length == 0}*/}
          {/*  type="card"*/}
          {/*  activeKey="optional-attributes"*/}
          {/*  style={{ margin: '10px 0' }}*/}
          {/*  items={[*/}
          {/*    {*/}
          {/*      key: 'optional-attributes',*/}
          {/*      label: '选填属性',*/}
          {/*      children: (*/}
          {/*        <Row key={'2'} hidden={collapse}>*/}
          {/*          {optionalItems}*/}
          {/*        </Row>*/}
          {/*      ),*/}
          {/*    },*/}
          {/*  ]}*/}
          {/*  tabBarExtraContent={*/}
          {/*    <Button*/}
          {/*      type={'link'}*/}
          {/*      disabled={false}*/}
          {/*      icon={collapse ? <DownOutlined /> : <UpOutlined />}*/}
          {/*      onClick={() => {*/}
          {/*        setCollapse(!collapse);*/}
          {/*      }}*/}
          {/*    >*/}
          {/*      {collapse ? '展开全部' : '收起全部'}*/}
          {/*    </Button>*/}
          {/*  }*/}
          {/*/>*/}
        </Form>
      </Modal>
    </>
  );
};
