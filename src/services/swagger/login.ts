// @ts-ignore
/* eslint-disable */
import request from '@/utils/framework/request';

/** 双因子获取手机验证码 POST /sys/getCaptcha */
export async function getCaptcha(body: API.SysCaptchaDto, options?: { [key: string]: any }) {
  return request<API.R>('/sys/getCaptcha', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取图片验证码 GET /sys/getImageVerifyCode */
export async function getImageVerifyCode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getImageVerifyCodeParams,
  options?: { [key: string]: any },
) {
  return request<any>('/sys/getImageVerifyCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前用户权限信息 GET /sys/getPermissions */
export async function getPermissions(options?: { [key: string]: any }) {
  return request<API.R>('/sys/getPermissions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取手机登录验证码 GET /sys/getPhoneCaptcha */
export async function getPhoneCaptcha(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getPhoneCaptchaParams,
  options?: { [key: string]: any },
) {
  return request<API.R>('/sys/getPhoneCaptcha', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取短信重置密码 GET /sys/getPhonePwd */
export async function getPhonePwd(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getPhonePwdParams,
  options?: { [key: string]: any },
) {
  return request<API.R>('/sys/getPhonePwd', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通过userId获取短信重置密码 GET /sys/getPhonePwdById */
export async function getPhonePwdById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getPhonePwdByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.R>('/sys/getPhonePwdById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前用户信息 GET /sys/getUserInfo */
export async function getUserInfo(options?: { [key: string]: any }) {
  return request<API.R>('/sys/getUserInfo', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 登录接口 POST /sys/login */
export async function login(body: API.SysLoginModel, options?: { [key: string]: any }) {
  return request<API.R>('/sys/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 双因子获取手机号 POST /sys/loginCaptcha */
export async function loginCaptcha(body: API.SysLoginDto, options?: { [key: string]: any }) {
  return request<API.R>('/sys/loginCaptcha', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 双因子登录 POST /sys/loginNew */
export async function loginNew(body: API.SysLoginCaptchaDto, options?: { [key: string]: any }) {
  return request<API.R>('/sys/loginNew', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户登出 GET /sys/loginOut */
export async function loginOut(options?: { [key: string]: any }) {
  return request<API.R>('/sys/loginOut', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 手机号登录接口 POST /sys/phoneLogin */
export async function phoneLogin(body: API.SysPhoneLoginModel, options?: { [key: string]: any }) {
  return request<API.R>('/sys/phoneLogin', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 单点登录 GET /sys/sso/login */
export async function sso(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ssoParams,
  options?: { [key: string]: any },
) {
  return request<API.R>('/sys/sso/login', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
