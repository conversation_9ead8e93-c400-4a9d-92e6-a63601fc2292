/* 密码安全要求
1. 口令长度必须达到8位，长度限制为8-20个字符。

2. 口令应由大写字母、小写字母、数字、特殊符号中至少3种组成。

3. 口令中不得包含内部邮箱账号前缀，不得包含姓名与工号的组合或账号相同的字母组合，含大小写组合或相似的组合。（如账号为guozw，密码为guoZW#16）

4. 不得使用有规律易被猜到的口令或与操作系统、数据库等相关的词组作为口令。（如root、admin、mysql、oracle、system）

5. 不得使用看似符合要求，实为3位及以上连续键盘序列组合作为口令。（如：123qweASD，1qaz\@WSX等）

6. 口令中不得包含姓名拼音（包括缩写）、生日、身份证号后4位、手机号后4位等信息。

7. 口令应不允许有\和英文" 。

8. 密码有效期最多不超过三个月。
*/
let REG_NUMBER = /.*\d+.*/;
let REG_UPPERCASE = /.*[A-Z].*/;
let REG_LOWERCASE = /.*[a-z].*/;
// eslint-disable-next-line no-useless-escape
let REG_SYMBOL = /.*[~!@#$%^&*()_+|<>,.?/:;\'\[\]{}"].*/;
/**
 * 键盘字符表(小写)
 * 非shift键盘字符表
 */
let CHAR_TABLE1 = [
  ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', '\0'],
  ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
  ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", '\0', '\0'],
  ['z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', '\0', '\0', '\0'],
];

/**
 * shift键盘的字符表
 */
let CHAR_TABLE2 = [
  ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '\0'],
  ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '{', '}', '|'],
  ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ':', '"', '\0', '\0'],
  ['z', 'x', 'c', 'v', 'b', 'n', 'm', '<', '>', '?', '\0', '\0', '\0'],
];
let checkPasswordRule = function (password, username) {
  let errMsg = '';
  if (!password || password.length < 8 || password.length > 20) {
    errMsg = '长度小于8或大于20';
  }
  if (username && password.toLowerCase().includes(username.toLowerCase())) {
    errMsg = '密码包含用户名';
  }
  if (isContinuousChar(password)) {
    errMsg = '包含3个及以上相同或字典连续字符';
  }
  if (isKeyBoardContinuousChar(password)) {
    errMsg = '包含3个及以上键盘连续字符';
  }
  let i = 0;
  if (REG_NUMBER.test(password)) i++;
  if (REG_LOWERCASE.test(password)) i++;
  if (REG_UPPERCASE.test(password)) i++;
  if (REG_SYMBOL.test(password)) i++;
  if (i < 3) {
    errMsg = '数字、小写字母、大写字母、特殊字符，至少包含三种';
  }
  return errMsg;
};

function isContinuousChar(password) {
  let chars = password.split('');
  for (let i = 0; i < chars.length - 2; i++) {
    let n1 = chars[i];
    let n2 = chars[i + 1];
    let n3 = chars[i + 2];
    // 判断重复字符
    if (n1 == n2 && n1 == n3) {
      return true;
    }
    // 判断连续字符： 正序 + 倒序
    if ((n1 + 1 == n2 && n1 + 2 == n3) || (n1 - 1 == n2 && n1 - 2 == n3)) {
      return true;
    }
  }
  return false;
}

/**
 * 是否包含3个及以上键盘连续字符
 *
 * @param password
 */
function isKeyBoardContinuousChar(password) {
  //考虑大小写，都转换成小写字母
  let lpStrChars = password.toLowerCase().split('');

  // 获取字符串长度
  let nStrLen = lpStrChars.length;
  // 定义位置数组：row - 行，col - column 列
  let pRowCharPos = [];
  let pColCharPos = [];
  for (let i = 0; i < nStrLen; i++) {
    let chLower = lpStrChars[i];
    pColCharPos[i] = -1;
    // 检索在表1中的位置，构建位置数组
    for (let nRowTable1Idx = 0; nRowTable1Idx < 4; nRowTable1Idx++) {
      for (let nColTable1Idx = 0; nColTable1Idx < 13; nColTable1Idx++) {
        if (chLower == CHAR_TABLE1[nRowTable1Idx][nColTable1Idx]) {
          pRowCharPos[i] = nRowTable1Idx;
          pColCharPos[i] = nColTable1Idx;
        }
      }
    }
    // 在表1中没找到，到表二中去找，找到则continue
    if (pColCharPos[i] >= 0) {
      continue;
    }
    // 检索在表2中的位置，构建位置数组
    for (let nRowTable2Idx = 0; nRowTable2Idx < 4; nRowTable2Idx++) {
      for (let nColTable2Idx = 0; nColTable2Idx < 13; nColTable2Idx++) {
        if (chLower == CHAR_TABLE2[nRowTable2Idx][nColTable2Idx]) {
          pRowCharPos[i] = nRowTable2Idx;
          pColCharPos[i] = nColTable2Idx;
        }
      }
    }
  }

  // 匹配坐标连线
  for (let j = 1; j <= nStrLen - 2; j++) {
    //同一行
    if (pRowCharPos[j - 1] == pRowCharPos[j] && pRowCharPos[j] == pRowCharPos[j + 1]) {
      // 键盘行正向连续（asd）或者键盘行反向连续（dsa）
      if (
        (pColCharPos[j - 1] + 1 == pColCharPos[j] && pColCharPos[j] + 1 == pColCharPos[j + 1]) ||
        (pColCharPos[j + 1] + 1 == pColCharPos[j] && pColCharPos[j] + 1 == pColCharPos[j - 1])
      ) {
        return true;
      }
    }
    //同一列
    if (pColCharPos[j - 1] == pColCharPos[j] && pColCharPos[j] == pColCharPos[j + 1]) {
      //键盘列连续（qaz）或者键盘列反向连续（zaq）
      if (
        (pRowCharPos[j - 1] + 1 == pRowCharPos[j] && pRowCharPos[j] + 1 == pRowCharPos[j + 1]) ||
        (pRowCharPos[j - 1] - 1 == pRowCharPos[j] && pRowCharPos[j] - 1 == pRowCharPos[j + 1])
      ) {
        return true;
      }
    }
  }
  return false;
}
export default checkPasswordRule;
